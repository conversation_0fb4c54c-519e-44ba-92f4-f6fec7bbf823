@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #2563eb;
  --secondary-color: #1d4ed8;
  --background-light: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --sidebar-width: 280px;
  --dashboard-bg: #0f172a;
  --sidebar-bg: rgba(67, 56, 202, 0.15);
  --card-bg: rgba(67, 56, 202, 0.1);
  --neon-purple: #a855f7;
  --neon-blue: #3b82f6;

  /* Configurações de aparência do chat */
  --chat-font-size: 14px;
  --chat-font-family: 'Inter', sans-serif;

  /* Z-index layers */
  --z-modal: 9999;
  --z-modal-content: 10000;
}

@layer components {
  .modal-overlay {
    z-index: 99999 !important;
    position: fixed !important;
  }

  .modal-content {
    z-index: 100000 !important;
    position: relative !important;
  }
}

@layer base {
  html, body {
    @apply bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-slate-100;
    background-attachment: fixed;
    height: 100%;
    overflow: hidden;
  }

  /* Scrollbar personalizada */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-slate-800/30;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-slate-600/50 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-slate-500/70;
  }

  /* Slider personalizado para configurações */
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    cursor: pointer;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    border: 2px solid rgba(59, 130, 246, 0.3);
  }

  .slider::-webkit-slider-thumb:hover {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
    transform: scale(1.1);
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    cursor: pointer;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    border: 2px solid rgba(59, 130, 246, 0.3);
  }

  /* Scrollbar personalizada para configurações */
  .custom-scrollbar::-webkit-scrollbar {
    width: 12px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(30, 58, 138, 0.3);
    border-radius: 6px;
    margin: 4px 0;
    border: 1px solid rgba(59, 130, 246, 0.1);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 6px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    transition: all 0.2s ease;
    min-height: 30px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.7);
    border-color: rgba(59, 130, 246, 0.4);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #1d4ed8, #1e3a8a);
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.9);
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: rgba(30, 58, 138, 0.3);
  }

  /* Scrollbar para Firefox */
  .custom-scrollbar {
    scrollbar-width: auto;
    scrollbar-color: #3b82f6 rgba(30, 58, 138, 0.3);
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse-glow {
  0%, 100% { filter: drop-shadow(0 0 5px rgba(168, 85, 247, 0.4)); }
  50% { filter: drop-shadow(0 0 15px rgba(168, 85, 247, 0.7)); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
  50% { box-shadow: 0 0 40px rgba(99, 102, 241, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes thinking-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

@keyframes energy-flow {
  0% {
    transform: translateX(-100%) rotate(0deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) rotate(360deg);
    opacity: 0;
  }
}

@keyframes progress-wave {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes dot-sequence {
  0%, 20% {
    transform: scale(1);
    background-color: rgb(59 130 246 / 0.6);
  }
  40% {
    transform: scale(1.3);
    background-color: rgb(34 211 238);
  }
  60%, 100% {
    transform: scale(1);
    background-color: rgb(59 130 246 / 0.6);
  }
}

@keyframes avatar-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 30px rgba(34, 211, 238, 0.6);
  }
}

@layer utilities {
  .bg-size-200 {
    background-size: 200% 200%;
  }
  .bg-pos-0 {
    background-position: 0% 0%;
  }
  .bg-pos-100 {
    background-position: 100% 100%;
  }
  .text-glow {
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.7), 0 0 20px rgba(168, 85, 247, 0.3);
  }
  .text-glow-sm {
    text-shadow: 0 0 5px rgba(168, 85, 247, 0.5);
  }
  .text-glow-blue {
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.7), 0 0 20px rgba(59, 130, 246, 0.4);
  }
  .neon-border {
    box-shadow: 0 0 5px rgba(168, 85, 247, 0.5), inset 0 0 5px rgba(168, 85, 247, 0.3);
  }
  .neon-border-blue {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5), inset 0 0 5px rgba(59, 130, 246, 0.3);
  }
  .glass-effect {
    backdrop-filter: blur(12px);
    background: rgba(30, 41, 59, 0.7);
  }
  .glass-effect-light {
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.05);
  }
  .glass-card {
    @apply rounded-xl border border-indigo-500/20 backdrop-blur-md bg-indigo-950/30;
  }
  .animated-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 8s ease infinite;
  }
  .shimmer {
    position: relative;
    overflow: hidden;
  }
  .shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.03) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 2s infinite;
  }
  .floating {
    animation: float 6s ease-in-out infinite;
  }
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
  .thinking-pulse {
    animation: thinking-pulse 2s ease-in-out infinite;
  }
  .energy-flow {
    animation: energy-flow 3s linear infinite;
  }
  .progress-wave {
    animation: progress-wave 2s ease-in-out infinite;
  }
  .dot-sequence-1 {
    animation: dot-sequence 1.6s ease-in-out infinite;
  }
  .dot-sequence-2 {
    animation: dot-sequence 1.6s ease-in-out infinite 0.2s;
  }
  .dot-sequence-3 {
    animation: dot-sequence 1.6s ease-in-out infinite 0.4s;
  }
  .dot-sequence-4 {
    animation: dot-sequence 1.6s ease-in-out infinite 0.6s;
  }
  .avatar-glow {
    animation: avatar-glow 3s ease-in-out infinite;
  }
}

@layer components {
  .app-container {
    @apply flex min-h-screen;
  }

  .sidebar {
    width: var(--sidebar-width);
    @apply fixed top-0 h-full bg-white border-r border-gray-200 flex flex-col start-0;
  }

  .sidebar-header {
    @apply p-4 border-b border-gray-200;
  }

  .sidebar-logo {
    @apply text-xl font-bold text-blue-600 flex items-center gap-2;
  }

  .sidebar-nav {
    @apply flex-1 p-4 space-y-2;
  }

  .nav-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors;
  }

  .nav-item.active {
    @apply bg-blue-50 text-blue-600;
  }

  .nav-item-icon {
    @apply w-5 h-5;
  }

  .main-content {
    margin-left: var(--sidebar-width);
    @apply flex-1 min-h-screen;
  }

  .chat-container {
    @apply max-w-4xl mx-auto p-6 h-[calc(100vh-2rem)] flex flex-col;
  }

  .chat-header {
    @apply mb-6 flex items-center justify-between;
  }

  .messages-container {
    @apply flex-1 overflow-y-auto space-y-4 p-4 rounded-lg bg-white shadow-sm border border-gray-100;
  }

  .message {
    @apply p-4 rounded-lg max-w-[80%] shadow-sm;
  }

  .user-message {
    @apply bg-blue-500 text-white ml-auto;
  }

  .bot-message {
    @apply bg-gray-100 text-gray-800;
  }

  .input-container {
    @apply mt-4 flex gap-2 bg-white p-4 rounded-lg border border-gray-200 shadow-sm;
  }

  .chat-input {
    @apply flex-1 p-3 rounded-lg bg-gray-50 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  .send-button {
    @apply px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium;
  }

  /* Animation for copy notification */
  @keyframes fade-in-out {
    0% {
      opacity: 0;
      transform: translateY(-10px);
    }
    20%, 80% {
      opacity: 1;
      transform: translateY(0);
    }
    100% {
      opacity: 0;
      transform: translateY(-10px);
    }
  }

  .animate-fade-in-out {
    animation: fade-in-out 2s ease-in-out;
  }

  /* Markdown Code Highlighting Overrides */
  .hljs {
    background: rgba(15, 23, 42, 0.8) !important;
    color: #e2e8f0 !important;
    border-radius: 0 !important;
  }

  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-literal,
  .hljs-section,
  .hljs-link {
    color: #a78bfa !important;
  }

  .hljs-string,
  .hljs-title,
  .hljs-name,
  .hljs-type,
  .hljs-attribute,
  .hljs-symbol,
  .hljs-bullet,
  .hljs-addition,
  .hljs-variable,
  .hljs-template-tag,
  .hljs-template-variable {
    color: #34d399 !important;
  }

  .hljs-comment,
  .hljs-quote,
  .hljs-deletion,
  .hljs-meta {
    color: #64748b !important;
  }

  .hljs-number,
  .hljs-regexp,
  .hljs-literal {
    color: #fbbf24 !important;
  }

  .hljs-built_in,
  .hljs-builtin-name,
  .hljs-class .hljs-title {
    color: #60a5fa !important;
  }

  .hljs-function .hljs-title {
    color: #f472b6 !important;
  }

  .hljs-tag {
    color: #e2e8f0 !important;
  }

  .hljs-attr {
    color: #fbbf24 !important;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: bold;
  }

  .user-profile {
    @apply mt-auto p-4 border-t border-gray-200 flex items-center gap-3;
  }

  .profile-avatar {
    @apply w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600;
  }

  .profile-info {
    @apply flex-1;
  }

  .profile-name {
    @apply font-medium text-gray-800;
  }

  .profile-status {
    @apply text-sm text-gray-500;
  }
  
  /* Dashboard specific components */
  .dashboard-sidebar {
    @apply h-screen w-72 bg-gradient-to-b from-indigo-950 via-indigo-900/90 to-indigo-950/90 backdrop-blur-md text-white flex flex-col shadow-xl border-r border-indigo-700/20;
    z-index: 50;
  }

  /* Message content word breaking */
  .message-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    max-width: 100%;
  }

  /* Ensure chat interface respects sidebar space */
  .chat-interface-container {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
  }

  .chat-interface-container * {
    box-sizing: border-box;
    max-width: 100%;
  }

  /* Force text content to break properly */
  .message-bubble-content {
    max-width: 100%;
    overflow-wrap: anywhere;
    word-break: break-word;
  }

  /* Ensure all message containers respect boundaries */
  .message-bubble-content p,
  .message-bubble-content div,
  .message-bubble-content span {
    max-width: 100%;
    overflow-wrap: anywhere;
    word-break: break-word;
  }

  /* Melhorar seleção de texto nas mensagens */
  .message-bubble-content {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }

  /* Garantir que elementos dentro das mensagens sejam selecionáveis */
  .message-bubble-content *,
  .message-bubble-content p,
  .message-bubble-content div,
  .message-bubble-content span,
  .message-bubble-content code,
  .message-bubble-content pre {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
  }

  /* Melhorar visibilidade da seleção de texto */
  .message-bubble-content ::selection {
    background-color: rgba(59, 130, 246, 0.3) !important;
    color: inherit !important;
  }

  .message-bubble-content ::-moz-selection {
    background-color: rgba(59, 130, 246, 0.3) !important;
    color: inherit !important;
  }

  /* Prevenir que botões de ação interfiram com a seleção */
  .message-bubble-content .message-actions {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
  }
  
  .feature-card {
    @apply glass-card p-6 hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300 transform hover:scale-[1.02] hover:border-indigo-400/30;
  }
  
  .glow-button {
    @apply px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg shadow-lg hover:shadow-purple-500/30 text-white font-medium transition-all duration-300 transform hover:scale-105 hover:from-purple-500 hover:to-indigo-500;
  }
  
  .neon-text {
    @apply font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-600 filter drop-shadow-sm text-glow animated-gradient;
  }

  /* KaTeX specific styles to ensure proper rendering */
  .katex {
    font-size: 1em !important;
    color: inherit !important;
  }

  .katex-display {
    margin: 1em 0 !important;
    text-align: center !important;
  }

  .katex .base {
    color: inherit !important;
  }

  .katex .mord,
  .katex .mop,
  .katex .mbin,
  .katex .mrel,
  .katex .mopen,
  .katex .mclose,
  .katex .mpunct,
  .katex .minner {
    color: inherit !important;
  }

  /* Ensure KaTeX math elements are properly styled */
  .katex .mathdefault {
    font-family: KaTeX_Math, "Times New Roman", serif !important;
  }

  .katex .mathit {
    font-family: KaTeX_Math, "Times New Roman", serif !important;
    font-style: italic !important;
  }

  .katex .mathrm {
    font-family: KaTeX_Main, "Times New Roman", serif !important;
    font-style: normal !important;
  }

  .katex .mathbf {
    font-family: KaTeX_Main, "Times New Roman", serif !important;
    font-weight: bold !important;
  }

  .katex .mathcal {
    font-family: KaTeX_Caligraphic, "Times New Roman", serif !important;
  }

  .katex .mathfrak {
    font-family: KaTeX_Fraktur, "Times New Roman", serif !important;
  }

  .katex .mathbb {
    font-family: KaTeX_AMS, "Times New Roman", serif !important;
  }

  .katex .mathscr {
    font-family: KaTeX_Script, "Times New Roman", serif !important;
  }

  .katex .mathsf {
    font-family: KaTeX_SansSerif, "Times New Roman", serif !important;
  }

  .katex .mathtt {
    font-family: KaTeX_Typewriter, "Times New Roman", serif !important;
  }

  /* Mobile-specific improvements */
  @media (max-width: 640px) {
    /* Reduce scrollbar width on mobile */
    ::-webkit-scrollbar {
      width: 4px;
    }

    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
    }

    /* Better touch targets */
    button, .btn, [role="button"] {
      min-height: 44px;
      min-width: 44px;
    }

    /* Improved text readability */
    body {
      -webkit-text-size-adjust: 100%;
      text-size-adjust: 100%;
    }

    /* Better form inputs on mobile */
    input, textarea, select {
      font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Adjust sidebar for mobile */
    .dashboard-sidebar {
      @apply w-full;
    }

    /* Better spacing for mobile */
    .chat-container {
      @apply p-3 h-[calc(100vh-1rem)];
    }

    /* Smaller text shadows for mobile performance */
    .text-glow {
      text-shadow: 0 0 5px rgba(168, 85, 247, 0.5);
    }

    .text-glow-blue {
      text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }

    /* Reduce animation intensity on mobile */
    .floating {
      animation-duration: 8s;
    }

    .pulse-glow {
      animation-duration: 3s;
    }

    /* Better modal sizing on mobile */
    .modal-content {
      @apply mx-3 my-6 max-h-[calc(100vh-3rem)];
    }
  }

  /* Tablet-specific improvements */
  @media (min-width: 641px) and (max-width: 1024px) {
    .dashboard-sidebar {
      @apply w-64;
    }

    .chat-container {
      @apply p-4;
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .text-glow, .text-glow-blue {
      text-shadow: 0 0 3px currentColor;
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .floating,
    .pulse-glow,
    .thinking-pulse,
    .energy-flow,
    .progress-wave,
    .dot-sequence-1,
    .dot-sequence-2,
    .dot-sequence-3,
    .dot-sequence-4,
    .avatar-glow,
    .animated-gradient {
      animation: none;
    }

    .shimmer::before {
      animation: none;
    }
  }
}
