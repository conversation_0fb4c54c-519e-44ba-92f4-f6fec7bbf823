import { NextRequest, NextResponse } from 'next/server';
import { httpsCallable } from 'firebase/functions';
import { functions, auth } from '@/lib/firebase';
import { signInWithCustomToken } from 'firebase/auth';
import { v4 as uuidv4 } from 'uuid';

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  attachments?: any[];
}

interface StreamRequest {
  messages: ChatMessage[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
  latexInstructions?: boolean;
  webSearchEnabled?: boolean;
  streaming: boolean;
  systemPrompt?: string;
  context?: string;
}

interface StreamData {
  userId: string;
  controller: ReadableStreamDefaultController | null;
  aborted: boolean;
  startTime: number;
  request: StreamRequest;
}

// Store global para gerenciar streams ativos
declare global {
  var activeStreams: Map<string, any> | undefined;
}

const getActiveStreams = () => {
  if (!global.activeStreams) {
    global.activeStreams = new Map<string, StreamData>();
    console.log('🔧 Criando novo store de streams');
  }
  return global.activeStreams;
};

// Cleanup de streams antigos (mais de 10 minutos)
setInterval(() => {
  const now = Date.now();
  const activeStreams = getActiveStreams();
  for (const [streamId, data] of activeStreams.entries()) {
    if (now - data.startTime > 10 * 60 * 1000) { // 10 minutos
      activeStreams.delete(streamId);
    }
  }
}, 60000); // Verificar a cada minuto

// POST: Iniciar streaming
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Token de autorização necessário' }, { status: 401 });
    }

    const token = authHeader.substring(7);

    // Verificar se é um token válido do Firebase
    let userId = 'authenticated-user'; // fallback

    try {
      // Tentar autenticar com o token
      await signInWithCustomToken(auth, token);
      userId = auth.currentUser?.uid || 'authenticated-user';
      console.log('Usuário autenticado:', userId);
    } catch (error) {
      console.log('Usando autenticação simplificada para desenvolvimento');
      // Em desenvolvimento, continuar com userId padrão
    }

    const body: StreamRequest = await request.json();

    if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
      return NextResponse.json({ error: 'Messages são obrigatórias' }, { status: 400 });
    }

    const streamId = uuidv4();
    const activeStreams = getActiveStreams();

    // Criar entrada no store
    activeStreams.set(streamId, {
      userId,
      controller: null,
      aborted: false,
      startTime: Date.now(),
      request: body
    });

    console.log(`Stream ${streamId} criado. Total de streams ativos:`, activeStreams.size);

    // Iniciar processamento assíncrono
    setTimeout(() => {
      // Usar versão real com Firebase Functions
      processStreamingRequest(streamId, body);
    }, 100);

    return NextResponse.json({ streamId });

  } catch (error) {
    console.error('Erro ao iniciar streaming:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// GET: Conectar ao stream via SSE
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const streamId = url.searchParams.get('streamId');
    const token = url.searchParams.get('token');

    if (!streamId || !token) {
      return NextResponse.json({ error: 'streamId e token são obrigatórios' }, { status: 400 });
    }

    const activeStreams = getActiveStreams();
    console.log(`Tentando conectar ao stream ${streamId}. Streams disponíveis:`, Array.from(activeStreams.keys()));

    const streamData = activeStreams.get(streamId);
    if (!streamData) {
      console.log(`Stream ${streamId} não encontrado. Total de streams:`, activeStreams.size);
      return NextResponse.json({ error: 'Stream não encontrado' }, { status: 404 });
    }

    console.log(`Stream ${streamId} encontrado, estabelecendo conexão SSE`);

    // Criar stream de resposta
    const stream = new ReadableStream({
      start(controller) {
        streamData.controller = controller;

        // Enviar headers SSE
        const encoder = new TextEncoder();
        controller.enqueue(encoder.encode('data: {"type":"connected"}\n\n'));
      },
      cancel() {
        streamData.aborted = true;
        activeStreams.delete(streamId);
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, DELETE',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('Erro ao conectar ao stream:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// DELETE: Cancelar streaming
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const streamId = url.pathname.split('/').pop();

    if (!streamId) {
      return NextResponse.json({ error: 'streamId é obrigatório' }, { status: 400 });
    }

    const activeStreams = getActiveStreams();
    const streamData = activeStreams.get(streamId);
    if (streamData) {
      streamData.aborted = true;
      if (streamData.controller) {
        try {
          const encoder = new TextEncoder();
          streamData.controller.enqueue(encoder.encode('data: {"type":"cancelled"}\n\n'));
          streamData.controller.close();
        } catch (error) {
          console.error('Erro ao fechar controller:', error);
        }
      }
      activeStreams.delete(streamId);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Erro ao cancelar streaming:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// Função simulada para testar o streaming
async function processStreamingRequestSimulated(streamId: string, requestData: StreamRequest) {
  const activeStreams = getActiveStreams();
  const streamData = activeStreams.get(streamId);
  if (!streamData || streamData.aborted) {
    console.log(`Stream ${streamId} não encontrado ou abortado`);
    return;
  }

  console.log(`Iniciando processamento simulado para stream ${streamId}`);

  // Aguardar um pouco para simular processamento
  await new Promise(resolve => setTimeout(resolve, 1000));

  if (!streamData.controller || streamData.aborted) {
    console.log(`Controller não disponível para stream ${streamId}`);
    return;
  }

  const encoder = new TextEncoder();
  const words = ['Olá', 'mundo', 'este', 'é', 'um', 'teste', 'de', 'streaming!'];

  try {
    for (let i = 0; i < words.length; i++) {
      const currentStreamData = activeStreams.get(streamId);
      if (!currentStreamData || currentStreamData.aborted || !currentStreamData.controller) {
        break;
      }

      const word = words[i] + (i < words.length - 1 ? ' ' : '');

      const data = JSON.stringify({
        content: word,
        isComplete: false
      });

      currentStreamData.controller.enqueue(encoder.encode(`data: ${data}\n\n`));

      // Delay entre palavras
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Enviar mensagem de conclusão
    if (streamData.controller && !streamData.aborted) {
      const finalData = JSON.stringify({
        content: '',
        isComplete: true,
        metadata: { processingTime: 2000 }
      });

      streamData.controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
      streamData.controller.close();
    }
  } catch (error) {
    console.error(`Erro no streaming simulado para ${streamId}:`, error);
  } finally {
    activeStreams.delete(streamId);
    console.log(`Stream ${streamId} finalizado`);
  }
}

// Função para processar a requisição de streaming
async function processStreamingRequest(streamId: string, requestData: StreamRequest) {
  const activeStreams = getActiveStreams();
  const streamData = activeStreams.get(streamId);
  if (!streamData || streamData.aborted) {
    console.log(`Stream ${streamId} não encontrado ou abortado`);
    return;
  }

  console.log(`Iniciando processamento real para stream ${streamId}`);

  try {
    // Usar o sistema existente do RafthorIA para obter a resposta
    console.log('Chamando Firebase Functions com dados:', {
      messages: requestData.messages,
      model: requestData.model || 'google/gemini-2.5-flash',
      userId: streamData.userId
    });

    const chatCompletion = httpsCallable(functions, 'chatCompletion');
    const result = await chatCompletion({
      messages: requestData.messages,
      model: requestData.model || 'google/gemini-2.5-flash',
      temperature: requestData.temperature || 0.7,
      max_tokens: requestData.max_tokens || 2048,
      latexInstructions: requestData.latexInstructions || false,
      webSearchEnabled: requestData.webSearchEnabled || false,
      memories: [],
      systemPrompt: requestData.systemPrompt || "",
      context: requestData.context || ""
    });

    const response = result.data as any;

    if (!streamData.controller || streamData.aborted) {
      console.log(`Controller não disponível para stream ${streamId}`);
      return;
    }

    console.log('Resposta recebida do Firebase Functions:', response);

    const encoder = new TextEncoder();

    // Simular streaming com a resposta real da IA
    const content = response.choices?.[0]?.message?.content || '';
    console.log('Conteúdo para streaming:', content);

    if (!content) {
      throw new Error('Nenhum conteúdo recebido da IA');
    }

    // Dividir em palavras para simular streaming
    const words = content.split(' ');

    for (let i = 0; i < words.length; i++) {
      const currentStreamData = activeStreams.get(streamId);
      if (!currentStreamData || currentStreamData.aborted || !currentStreamData.controller) {
        break;
      }

      const word = words[i] + (i < words.length - 1 ? ' ' : '');

      try {
        const data = JSON.stringify({
          content: word,
          isComplete: false
        });

        currentStreamData.controller.enqueue(encoder.encode(`data: ${data}\n\n`));

        // Delay entre palavras para simular streaming
        await new Promise(resolve => setTimeout(resolve, 50));
      } catch (error) {
        console.error('Erro ao enviar chunk:', error);
        break;
      }
    }

    // Enviar mensagem de conclusão
    if (streamData.controller && !streamData.aborted) {
      try {
        const finalData = JSON.stringify({
          content: '',
          isComplete: true,
          metadata: {
            usedCoT: response.rafthoria_metadata?.usedCoT || false,
            confidence: response.rafthoria_metadata?.confidence || 0,
            processingTime: response.rafthoria_metadata?.processingTime || 0,
            model: response.model,
            usage: response.usage
          }
        });

        streamData.controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
        streamData.controller.close();
      } catch (error) {
        console.error('Erro ao finalizar stream:', error);
      }
    }

  } catch (error) {
    console.error('Erro no Firebase Functions, tentando fallback direto:', error);

    // Fallback: usar API do OpenRouter diretamente
    try {
      await processStreamingFallback(streamId, requestData);
    } catch (fallbackError) {
      console.error('Erro no fallback também:', fallbackError);

      if (streamData.controller && !streamData.aborted) {
        try {
          const encoder = new TextEncoder();
          const errorData = JSON.stringify({
            content: '',
            isComplete: true,
            error: 'Erro ao processar requisição'
          });

          streamData.controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
          streamData.controller.close();
        } catch (closeError) {
          console.error('Erro ao fechar stream com erro:', closeError);
        }
      }
    }
  } finally {
    activeStreams.delete(streamId);
  }
}

// Função de fallback que usa OpenRouter diretamente
async function processStreamingFallback(streamId: string, requestData: StreamRequest) {
  const activeStreams = getActiveStreams();
  const streamData = activeStreams.get(streamId);
  if (!streamData || streamData.aborted) {
    return;
  }

  console.log(`Usando fallback direto para OpenRouter - stream ${streamId}`);

  // Preparar dados para OpenRouter
  const openRouterData = {
    model: requestData.model || 'google/gemini-2.5-flash',
    messages: requestData.messages,
    temperature: requestData.temperature || 0.7,
    max_tokens: requestData.max_tokens || 2048,
    stream: true
  };

  // Usar uma API key padrão ou de ambiente
  const apiKey = process.env.OPENROUTER_API_KEY || 'sk-or-v1-your-key-here';

  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'RafthorIA'
      },
      body: JSON.stringify(openRouterData)
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Não foi possível obter reader do stream');
    }

    const decoder = new TextDecoder();
    const encoder = new TextEncoder();
    let fullContent = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Verificar se ainda está ativo
        const currentStreamData = activeStreams.get(streamId);
        if (!currentStreamData || currentStreamData.aborted || !currentStreamData.controller) {
          break;
        }

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              // Finalizar stream
              const finalData = JSON.stringify({
                content: '',
                isComplete: true,
                metadata: { processingTime: Date.now() - streamData.startTime }
              });

              currentStreamData.controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
              currentStreamData.controller.close();
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.choices?.[0]?.delta?.content) {
                const content = parsed.choices[0].delta.content;
                fullContent += content;

                const streamChunk = JSON.stringify({
                  content: content,
                  isComplete: false
                });

                currentStreamData.controller.enqueue(encoder.encode(`data: ${streamChunk}\n\n`));
              }
            } catch (parseError) {
              // Ignorar erros de parsing de chunks individuais
              continue;
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

  } catch (error) {
    console.error('Erro no fallback OpenRouter:', error);

    // Se falhar, usar a versão simulada como último recurso
    console.log('Usando versão simulada como último recurso');
    await processStreamingRequestSimulated(streamId, requestData);
  }
}
