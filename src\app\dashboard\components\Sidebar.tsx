'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useConversations } from '@/contexts/ConversationContext';
import { useChatFolders } from '@/contexts/ChatFolderContext';
import { useOpenRouterBalance } from '@/hooks/useOpenRouterBalance';
import { adminService } from '@/lib/adminService';
import { deleteConversation, updateConversationFixedStatus } from '@/lib/conversationService';
import AdminAccessModal from './AdminAccessModal';
import { format, formatDistanceToNow, isToday, isYesterday } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Lock } from 'lucide-react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core';
import ChatFolder from './ChatFolder';
import DraggableConversation from './DraggableConversation';
import DroppableFolder from './DroppableFolder';
import { Conversation } from '@/lib/conversationService';
import { ChatFolder as ChatFolderType } from '@/lib/types/chatFolder';

interface SidebarProps {
  onNewConversationClick: () => void;
  onEditConversationClick?: (conversationId: string) => void;
  onDeleteConversationClick?: (conversationId: string, conversationName: string) => void;
  onCreateFolderClick: () => void;
  onEditFolderClick: (folderId: string) => void;
  onDeleteFolderClick: (folderId: string) => void;
  onPasswordRequired?: (conversation: Conversation) => void;
  onGoToTemporaryChat?: () => void;
}

export default function Sidebar({
  onNewConversationClick,
  onEditConversationClick,
  onDeleteConversationClick,
  onCreateFolderClick,
  onEditFolderClick,
  onDeleteFolderClick,
  onPasswordRequired,
  onGoToTemporaryChat
}: SidebarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [draggedConversation, setDraggedConversation] = useState<Conversation | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [showAdminModal, setShowAdminModal] = useState(false);
  const [groupedConversations, setGroupedConversations] = useState<{
    folderedConversations: { [folderId: string]: Conversation[] };
    unorganizedConversations: Conversation[];
  }>({
    folderedConversations: {},
    unorganizedConversations: []
  });

  const router = useRouter();
  const { user, userData, logout } = useAuth();
  const { balance, loading: balanceLoading, error: balanceError } = useOpenRouterBalance();
  const {
    conversations,
    selectedConversation,
    loading: isLoading,
    selectConversation,
    selectConversationWithPassword,
    removeConversation,
    refreshConversations,
    updateConversation,
    getGroupedConversations
  } = useConversations();

  const {
    folders,
    loading: foldersLoading,
    expandedFolders,
    createFolder,
    updateFolder,
    deleteFolder,
    toggleFolderExpansion,
    moveConversationToFolder,
    refreshFolders
  } = useChatFolders();

  const activeConversationId = selectedConversation?.id || null;

  // Função para lidar com seleção de conversa (com verificação de senha)
  const handleSelectConversation = async (conversation: Conversation) => {
    const success = await selectConversationWithPassword(conversation);
    if (!success && onPasswordRequired) {
      // Conversa protegida por senha - notificar componente pai
      onPasswordRequired(conversation);
    }
  };

  // Configurar sensores para drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Mínimo de 8px de movimento para iniciar drag
        delay: 200, // Delay de 200ms antes de iniciar drag
        tolerance: 5, // Tolerância de 5px para movimento
      },
      // Prevenir interferência com seleção de texto nas mensagens
      onActivation: (event) => {
        // Só prevenir comportamento padrão se o elemento clicado for parte da sidebar
        const target = event.target as Element;
        const isInSidebar = target.closest('.sidebar-container') || target.closest('[data-dnd-context="sidebar"]');
        if (isInSidebar) {
          event.preventDefault();
        }
      },
    })
  );

  // Verificar se o usuário é admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (user) {
        try {
          const adminStatus = await adminService.isCurrentUserAdmin();
          setIsAdmin(adminStatus);
        } catch (error) {
          console.error('Erro ao verificar status de admin:', error);
          setIsAdmin(false);
        }
      } else {
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  // Carregar conversas agrupadas quando as conversas ou pastas mudarem
  useEffect(() => {
    const loadGroupedConversations = async () => {
      try {
        console.log(`📂 Carregando conversas agrupadas... Total: ${conversations.length}`);
        const grouped = await getGroupedConversations();
        setGroupedConversations(grouped);
        console.log(`📂 Conversas agrupadas carregadas:`, {
          folderedCount: Object.keys(grouped.folderedConversations).length,
          unorganizedCount: grouped.unorganizedConversations.length
        });
      } catch (error) {
        console.error('Error loading grouped conversations:', error);
      }
    };

    if (user && !isLoading && !foldersLoading) {
      loadGroupedConversations();
    }
  }, [user, conversations, folders, isLoading, foldersLoading]);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const handleDeleteConversation = async (conversationId: string, e: React.MouseEvent) => {
    e.stopPropagation();

    if (!user || !onDeleteConversationClick) return;

    // Encontrar o nome da conversa para exibir no modal
    const conversation = conversations.find(c => c.id === conversationId);
    const conversationName = conversation?.name || 'Esta conversa';

    onDeleteConversationClick(conversationId, conversationName);
  };

  const handlePinConversation = async (conversationId: string, e: React.MouseEvent) => {
    e.stopPropagation();

    if (!user) return;

    try {
      const conversation = conversations.find(c => c.id === conversationId);
      if (!conversation) return;

      const newFixedStatus = !conversation.isFixed;

      // Atualizar no Firestore
      await updateConversationFixedStatus(user.uid, conversationId, newFixedStatus);

      // Atualizar no contexto local
      updateConversation(conversationId, { isFixed: newFixedStatus });

      // Atualizar lista de conversas
      await refreshConversations();
    } catch (error) {
      console.error('Erro ao fixar/desafixar conversa:', error);
      alert('Erro ao fixar/desafixar conversa. Tente novamente.');
    }
  };

  const handleEditConversation = (conversationId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEditConversationClick) {
      onEditConversationClick(conversationId);
    }
  };

  // Handlers para drag and drop
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const conversation = conversations.find(c => c.id === active.id);
    if (conversation) {
      setDraggedConversation(conversation);
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedConversation(null);

    if (!over || !user) return;

    const conversationId = active.id as string;
    const overId = over.id as string;

    // Determinar o folderId de destino
    let targetFolderId: string | null = null;
    if (overId.startsWith('folder-')) {
      targetFolderId = overId.replace('folder-', '');
    } else if (overId === 'unorganized') {
      targetFolderId = null;
    } else {
      return; // Drop inválido
    }

    try {
      await moveConversationToFolder(conversationId, targetFolderId);
      await refreshConversations();
      await refreshFolders();
    } catch (error) {
      console.error('Error moving conversation:', error);
    }
  };

  // Handlers para gerenciamento de pastas
  const handleCreateFolder = () => {
    onCreateFolderClick();
  };

  const handleEditFolder = (folderId: string) => {
    onEditFolderClick(folderId);
  };

  const handleDeleteFolder = async (folderId: string) => {
    onDeleteFolderClick(folderId);
  };



  return (
    <>
      <aside className="h-full w-full sm:w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-r border-blue-700/30 flex flex-col overflow-hidden relative shadow-2xl sidebar-container" data-dnd-context="sidebar">
      {/* Efeito de brilho sutil */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"></div>

      {/* Header com perfil do usuário */}
      <div className="relative p-3 sm:p-4 lg:p-6 border-b border-blue-700/30 bg-gradient-to-r from-blue-900/50 to-blue-950/50">
        <div className="flex items-center justify-between">
          {/* Perfil do usuário */}
          <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
            {/* Foto de perfil */}
            {userData?.profilePhotoURL ? (
              <img
                src={userData.profilePhotoURL}
                alt="Foto de perfil"
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover shadow-lg flex-shrink-0"
              />
            ) : (
              <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center shadow-lg flex-shrink-0">
                <span className="text-white font-bold text-xs sm:text-sm">
                  {userData?.username?.charAt(0).toUpperCase() || user?.email?.charAt(0).toUpperCase() || '?'}
                </span>
              </div>
            )}

            {/* Nome e saldo */}
            <div className="flex-1 min-w-0">
              <p className="text-xs sm:text-sm font-semibold text-white truncate">
                {userData?.username || 'Usuário'}
              </p>
              <p className="text-xs text-blue-300/70 truncate hidden sm:block">
                {balanceLoading ? (
                  'Carregando saldo...'
                ) : balanceError ? (
                  'Saldo indisponível'
                ) : balance !== null ? (
                  `Saldo: $${balance.toFixed(2)}`
                ) : (
                  'Configure OpenRouter'
                )}
              </p>
            </div>
          </div>

          {/* Botão de configurações */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="p-1.5 sm:p-2 rounded-xl bg-blue-700/50 hover:bg-blue-600/70 transition-all duration-300 text-blue-300 hover:text-white shadow-lg hover:shadow-xl flex-shrink-0"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Dropdown Menu */}
      <div
        className={`absolute top-16 sm:top-20 right-2 sm:right-4 bg-blue-900/95 border border-blue-700/30 rounded-lg shadow-xl z-20 w-48 sm:w-56 transform origin-top-right transition-all duration-200 ${
          isMenuOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none'
        }`}
      >
        <div className="p-1.5 sm:p-2">
          <div className="px-3 sm:px-4 py-2 sm:py-3 border-b border-blue-700/30">
            <p className="text-xs sm:text-sm text-blue-200 truncate">
              {user?.email}
            </p>
            <p className="text-xs text-blue-300/70 mt-1">
              Plano BYOK
            </p>
          </div>
          <div className="mt-2">
            <button
              onClick={() => router.push('/dashboard/settings')}
              className="w-full text-left px-4 py-2 text-blue-200 hover:bg-blue-800/30 rounded flex items-center gap-2 text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Configurações
            </button>
            <button
              onClick={() => router.push('/dashboard/stats')}
              className="w-full text-left px-4 py-2 text-blue-200 hover:bg-blue-800/30 rounded flex items-center gap-2 text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Analytics
            </button>
            <button
              onClick={() => router.push('/dashboard/favorites')}
              className="w-full text-left px-4 py-2 text-blue-200 hover:bg-blue-800/30 rounded flex items-center gap-2 text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
              Favoritos
            </button>
            <button
              onClick={() => router.push('/dashboard/attachments')}
              className="w-full text-left px-4 py-2 text-blue-200 hover:bg-blue-800/30 rounded flex items-center gap-2 text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
              </svg>
              Anexos
            </button>

            {/* Botão de Admin (sempre visível para usuários logados) */}
            <button
              onClick={() => {
                setIsMenuOpen(false);
                setShowAdminModal(true);
              }}
              className="w-full text-left px-4 py-2 text-purple-300 hover:bg-purple-800/20 rounded flex items-center gap-2 text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              Área Admin
            </button>

            <div className="border-t border-blue-700/30 my-1"></div>
            <button
              onClick={handleLogout}
              className="w-full text-left px-4 py-2 text-red-400 hover:bg-red-900/20 rounded flex items-center gap-2 text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Sair
            </button>
          </div>
        </div>
      </div>

      {/* Button to overlay to close menu */}
      {isMenuOpen && (
        <div 
          className="fixed inset-0 z-10" 
          onClick={() => setIsMenuOpen(false)}
        ></div>
      )}

      {/* Action Buttons */}
      <div className="p-3 sm:p-4 lg:p-6 space-y-3">
        {/* Área Inicial Button */}
        <button
          onClick={() => {
            if (onGoToTemporaryChat) {
              onGoToTemporaryChat();
            }
          }}
          className="w-full bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white py-2.5 sm:py-3 lg:py-4 rounded-xl flex items-center justify-center gap-2 sm:gap-3 transition-all duration-300 shadow-xl shadow-blue-900/30 hover:shadow-blue-900/50 hover:scale-[1.02] font-semibold text-sm sm:text-base"
          title="Voltar para a área de experimentação temporária"
        >
          <div className="p-0.5 sm:p-1 rounded-lg bg-white/20">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
          </div>
          <span className="hidden sm:inline">Área Inicial</span>
          <span className="sm:hidden">Início</span>
        </button>

        {/* New Conversation Button */}
        <button
          onClick={onNewConversationClick}
          className="w-full bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white py-2.5 sm:py-3 lg:py-4 rounded-xl flex items-center justify-center gap-2 sm:gap-3 transition-all duration-300 shadow-xl shadow-blue-900/30 hover:shadow-blue-900/50 hover:scale-[1.02] font-semibold text-sm sm:text-base"
          title="Criar uma nova conversa permanente"
        >
          <div className="p-0.5 sm:p-1 rounded-lg bg-white/20">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <span className="hidden sm:inline">Nova Conversa</span>
          <span className="sm:hidden">Nova</span>
        </button>
      </div>

      {/* Título das Conversas e Botão de Nova Pasta */}
      <div className="px-3 sm:px-4 lg:px-6 pb-2 flex items-center justify-between">
        <h3 className="text-xs sm:text-sm font-semibold text-blue-300 uppercase tracking-wider">
          Conversas
        </h3>
        <button
          onClick={handleCreateFolder}
          className="p-1 sm:p-1.5 rounded-lg bg-blue-700/50 hover:bg-blue-600/70 text-blue-300 hover:text-white transition-all duration-200"
          title="Nova pasta"
        >
          <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
      </div>

      {/* Lista de Conversas com Drag and Drop */}
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex-1 overflow-y-auto py-2 scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent">
          {(isLoading || foldersLoading) ? (
            // Skeleton loading
            <div className="px-2 sm:px-4 space-y-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse flex h-12 sm:h-14 items-center px-2 sm:px-3 rounded-lg">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-900/50 rounded mr-2 sm:mr-3 flex-shrink-0"></div>
                  <div className="flex-1 min-w-0">
                    <div className="h-2.5 sm:h-3 bg-blue-900/50 rounded mb-1.5 sm:mb-2 w-2/3"></div>
                    <div className="h-2 bg-blue-900/30 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : conversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center px-6">
            <div className="w-16 h-16 mb-4 rounded-full bg-blue-900/30 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-blue-300 font-medium mb-1">Nenhuma conversa</h3>
            <p className="text-blue-400/70 text-sm">
              Clique em "Nova Conversa" para começar a interagir com a RafthorIA.
            </p>
          </div>
          ) : (
            <div className="px-2 space-y-3">
              {/* Pastas de Chats */}
              {folders.map((folder) => {
                const folderConversations = groupedConversations.folderedConversations[folder.id] || [];
                return (
                  <ChatFolder
                    key={folder.id}
                    folder={folder}
                    conversations={folderConversations}
                    isExpanded={expandedFolders.has(folder.id)}
                    onToggleExpansion={toggleFolderExpansion}
                    onEditFolder={handleEditFolder}
                    onDeleteFolder={handleDeleteFolder}
                    onSelectConversation={handleSelectConversation}
                    activeConversationId={activeConversationId}
                    onEditConversation={onEditConversationClick}
                    onDeleteConversation={onDeleteConversationClick}
                    onPinConversation={handlePinConversation}
                  />
                );
              })}

              {/* Conversas Sem Pasta */}
              {groupedConversations.unorganizedConversations.length > 0 && (
                <DroppableFolder className="space-y-2">
                  <div className="px-2 pb-2">
                    <h4 className="text-xs font-semibold text-blue-400 uppercase tracking-wider">
                      Sem pasta
                    </h4>
                  </div>
                  {groupedConversations.unorganizedConversations.map((conversation) => {
                    const isPinned = conversation.isFixed || false;
                    return (
                      <DraggableConversation
                        key={conversation.id}
                        conversation={conversation}
                        isActive={activeConversationId === conversation.id}
                      >
                        <div
                          className={`group relative rounded-xl transition-all duration-300 ${
                            activeConversationId === conversation.id
                              ? 'bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg'
                              : 'hover:bg-blue-800/30 border border-transparent'
                          }`}
                        >
                          <button
                            className="w-full text-left p-4 flex items-start space-x-3"
                            onClick={() => handleSelectConversation(conversation)}
                          >
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 transition-all duration-300 relative ${
                              activeConversationId === conversation.id
                                ? 'bg-gradient-to-br from-blue-500 to-cyan-600 shadow-lg'
                                : 'bg-blue-700/50 group-hover:bg-blue-600/70'
                            }`}>
                              {/* Ícone de conversa fixada */}
                              {isPinned && (
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full flex items-center justify-center">
                                  <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              )}

                              {/* Ícone de chat protegido por senha */}
                              {conversation.password && (
                                <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                                  <Lock className="w-2 h-2 text-white" />
                                </div>
                              )}
                              <svg
                                className={`h-5 w-5 ${
                                  activeConversationId === conversation.id
                                    ? 'text-white'
                                    : 'text-blue-300 group-hover:text-white'
                                }`}
                                fill="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                              </svg>
                            </div>

                            {/* Container do conteúdo com transição suave */}
                            <div className="overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-24">
                              <h4 className={`truncate text-sm font-semibold mb-1 ${
                                activeConversationId === conversation.id
                                  ? 'text-white'
                                  : 'text-blue-100 group-hover:text-white'
                              }`}>
                                {conversation.name}
                              </h4>
                              <p className={`truncate text-xs leading-relaxed transition-all duration-300 ${
                                activeConversationId === conversation.id
                                  ? 'text-blue-200/80'
                                  : 'text-blue-300/70 group-hover:text-blue-200'
                              }`}>
                                {conversation.ultimaMensagem || 'Nenhuma mensagem ainda...'}
                              </p>
                              {conversation.ultimaMensagemEm && (
                                <span className={`text-xs mt-1 block ${
                                  activeConversationId === conversation.id
                                    ? 'text-blue-300/60'
                                    : 'text-blue-400/60 group-hover:text-blue-300/80'
                                }`}>
                                  {formatDistanceToNow(new Date(conversation.ultimaMensagemEm.seconds * 1000), {
                                    addSuffix: true,
                                    locale: ptBR
                                  })}
                                </span>
                              )}
                            </div>
                          </button>

                          {/* Ícones de ação - aparecem apenas no hover */}
                          <div className={`absolute right-2 top-1/2 -translate-y-1/2 flex space-x-1 transition-all duration-300 ${
                            'opacity-0 group-hover:opacity-100 translate-x-2 group-hover:translate-x-0'
                          }`}>
                            {onEditConversationClick && (
                              <button
                                className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm"
                                title="Configurações"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onEditConversationClick(conversation.id);
                                }}
                              >
                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                              </button>
                            )}
                            <button
                              className={`p-1.5 rounded-lg transition-all duration-200 backdrop-blur-sm ${
                                isPinned
                                  ? 'bg-yellow-600/90 hover:bg-yellow-500/90 text-yellow-200 hover:text-white'
                                  : 'bg-blue-700/90 hover:bg-yellow-600/90 text-blue-300 hover:text-white'
                              }`}
                              title={isPinned ? "Desafixar conversa" : "Fixar conversa"}
                              onClick={(e) => handlePinConversation(conversation.id, e)}
                            >
                              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                              </svg>
                            </button>
                            {onDeleteConversationClick && (
                              <button
                                className="p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm"
                                title="Deletar"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onDeleteConversationClick(conversation.id, conversation.name);
                                }}
                              >
                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            )}
                          </div>
                        </div>
                      </DraggableConversation>
                    );
                  })}
                </DroppableFolder>
              )}
            </div>
          )}

          {/* Drag Overlay */}
          <DragOverlay>
            {draggedConversation ? (
              <div className="bg-blue-800/90 border border-blue-500/50 rounded-xl p-4 shadow-2xl backdrop-blur-sm">
                <h4 className="text-white font-semibold text-sm truncate">
                  {draggedConversation.name}
                </h4>
                <p className="text-blue-200/80 text-xs truncate mt-1">
                  Movendo conversa...
                </p>
              </div>
            ) : null}
          </DragOverlay>
        </div>
      </DndContext>

    </aside>

    {/* Modal de Acesso Admin */}
    <AdminAccessModal
      isOpen={showAdminModal}
      onClose={() => setShowAdminModal(false)}
    />
    </>
  );
}