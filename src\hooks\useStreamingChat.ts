'use client';

import { useState, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ChatMessage, AttachmentMetadata } from '@/lib/types/chat';
import { getUserSettings, getUserAPIEndpoints } from '@/lib/settingsService';
import { openRouterService } from '@/lib/openRouterService';

interface MessageContent {
  type: 'text' | 'image_url' | 'file';
  text?: string;
  image_url?: {
    url: string;
  };
  file?: {
    filename: string;
    file_data: string;
  };
}

interface StreamingResponse {
  content: string;
  isComplete: boolean;
  metadata?: {
    usedCoT: boolean;
    confidence: number;
    processingTime: number;
    model: string;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
      cost: number;
    };
    finishReason?: string;
  };
}

interface UseStreamingChatOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  latexInstructions?: boolean;
  webSearchEnabled?: boolean;
  chatId?: string;
  systemPrompt?: string;
  context?: string;
}

interface StreamingResult {
  success: boolean;
  response?: string;
  wasCancelled?: boolean;
  metadata?: StreamingResponse['metadata'];
}

// Função para calcular custo baseado nos tokens e modelo
const calculateCost = async (modelId: string, promptTokens: number, completionTokens: number): Promise<number> => {
  try {
    // Buscar informações do modelo
    const models = await openRouterService.fetchModels();
    const model = models.find((m: any) => m.id === modelId);

    if (!model) {
      console.warn('Modelo não encontrado para cálculo de custo:', modelId);
      return 0;
    }

    // Calcular custo (preços já estão em formato por milhão de tokens)
    const promptCost = (promptTokens / 1000000) * parseFloat(model.pricing.prompt);
    const completionCost = (completionTokens / 1000000) * parseFloat(model.pricing.completion);
    const totalCost = promptCost + completionCost;

    console.log('Cálculo de custo:', {
      modelId,
      promptTokens,
      completionTokens,
      promptPrice: model.pricing.prompt,
      completionPrice: model.pricing.completion,
      promptCost,
      completionCost,
      totalCost
    });

    return totalCost;
  } catch (error) {
    console.error('Erro ao calcular custo:', error);
    return 0;
  }
};

// Convert attachments to OpenRouter format
const convertAttachmentsToContent = async (
  text: string,
  attachments?: AttachmentMetadata[]
): Promise<string | MessageContent[]> => {
  console.log('convertAttachmentsToContent called with:', { text, attachments });

  if (!attachments || attachments.length === 0) {
    console.log('No attachments, returning text only');
    return text;
  }

  const content: MessageContent[] = [];

  // Add text content if present
  if (text.trim()) {
    content.push({
      type: 'text',
      text: text
    });
  }

  // Process attachments
  for (const attachment of attachments) {
    console.log('Processing attachment:', attachment);

    if (attachment.type === 'image') {
      console.log('Adding image attachment:', attachment.url);
      content.push({
        type: 'image_url',
        image_url: {
          url: attachment.url
        }
      });
    } else if (attachment.type === 'pdf') {
      console.log('Processing PDF attachment:', attachment.filename);

      if (attachment.base64Data) {
        console.log('Using stored base64 data, length:', attachment.base64Data.length);

        const fileContent: MessageContent = {
          type: 'file',
          file: {
            filename: attachment.filename,
            file_data: attachment.base64Data
          }
        };

        console.log('Adding file content:', fileContent);
        content.push(fileContent);
      } else {
        console.error('PDF attachment missing base64Data:', attachment.filename);
        // Skip this attachment if no base64 data
      }
    }
  }

  console.log('Final content array:', content);
  return content;
};

export const useStreamingChat = (options: UseStreamingChatOptions = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [streamingContent, setStreamingContent] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const { user } = useAuth();

  // Referência para controlar o cancelamento
  const abortControllerRef = useRef<AbortController | null>(null);

  const cancelStreaming = useCallback(() => {
    console.log('Cancelando streaming...');

    // Abortar requisição se existir
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setIsStreaming(false);
    setLoading(false);
  }, []);

  const generateStreamingResponse = useCallback(async (
    userMessage: string,
    conversationHistory: ChatMessage[] = [],
    attachments?: AttachmentMetadata[],
    customOptions?: Partial<UseStreamingChatOptions>
  ): Promise<StreamingResult> => {
    if (!user) {
      setError('Usuário não autenticado');
      return { success: false };
    }

    // Cancelar streaming anterior se existir
    cancelStreaming();

    setLoading(true);
    setError(null);
    setStreamingContent('');
    setIsStreaming(true);

    // Criar novo AbortController
    abortControllerRef.current = new AbortController();

    // Variáveis para armazenar dados do streaming
    let currentStreamContent = '';
    let usageData: any = null;
    let finishReason: string | null = null;
    let model: string | null = null;
    const startTime = Date.now();
    const finalOptions = { ...options, ...customOptions };

    try {

      // Preparar mensagens para envio com processamento de anexos
      const messages = [];

      // Processar histórico de conversação SEM anexos (apenas texto)
      for (const msg of conversationHistory) {
        // IMPORTANTE: Não incluir anexos do histórico, apenas o texto
        console.log(`📜 STREAMING HISTORY - Processing message without attachments for ${msg.role}`);
        messages.push({
          role: msg.role,
          content: msg.content // Apenas texto, sem anexos
        });
      }

      // Processar mensagem atual do usuário
      console.log('🚀 STREAMING CURRENT MESSAGE - Processing current user message with attachments:', attachments);
      console.log('🚀 STREAMING CURRENT MESSAGE - Number of attachments to process:', attachments?.length || 0);
      if (attachments && attachments.length > 0) {
        console.log('🚀 STREAMING CURRENT MESSAGE - Attachment IDs:', attachments.map(att => att.id));
        console.log('🚀 STREAMING CURRENT MESSAGE - Attachment filenames:', attachments.map(att => att.filename));
      }
      const currentMessageContent = await convertAttachmentsToContent(userMessage, attachments);
      messages.push({
        role: 'user' as const,
        content: currentMessageContent
      });

      console.log('Iniciando streaming REAL com OpenRouter');

      // Obter configurações do usuário para pegar a API key
      const userSettings = await getUserSettings(user.uid);
      if (!userSettings) {
        throw new Error('Configurações do usuário não encontradas');
      }

      // Buscar endpoint ativo do OpenRouter
      const endpoints = await getUserAPIEndpoints(user.uid);
      let activeEndpoint = endpoints.find(ep => ep.isActive && ep.name.toLowerCase().includes('openrouter'));

      // Se não encontrar OpenRouter, usar qualquer endpoint ativo que suporte streaming
      if (!activeEndpoint) {
        activeEndpoint = endpoints.find(ep => ep.isActive);
      }

      if (!activeEndpoint) {
        throw new Error('Nenhum endpoint de IA configurado. Configure um endpoint nas configurações.');
      }

      console.log('Usando endpoint:', activeEndpoint.name);

      // Determinar URL da API baseada no endpoint
      const apiUrl = `${activeEndpoint.endpoint}/chat/completions`;
      const headers: Record<string, string> = {
        'Authorization': `Bearer ${activeEndpoint.apiKey}`,
        'Content-Type': 'application/json'
      };

      // Adicionar headers específicos para OpenRouter
      if (activeEndpoint.name.toLowerCase().includes('openrouter')) {
        headers['HTTP-Referer'] = window.location.origin;
        headers['X-Title'] = 'RafthorIA';
      }

      // Determinar modelo final com web search
      let finalModel = finalOptions.model || 'google/gemini-2.5-flash';
      if (finalOptions.webSearchEnabled && activeEndpoint.name.toLowerCase().includes('openrouter')) {
        // Add :online suffix for OpenRouter web search
        finalModel = finalModel.includes(':online') ? finalModel : `${finalModel}:online`;
        console.log('Web search enabled for OpenRouter', {
          originalModel: finalOptions.model,
          finalModel: finalModel
        });
      }

      // Verificar se há PDFs nas mensagens
      const hasPDFs = messages.some((msg: any) => {
        if (Array.isArray(msg.content)) {
          return msg.content.some((content: any) => content.type === 'file');
        }
        return false;
      });

      // Preparar dados para streaming real
      const streamData: any = {
        model: finalModel,
        messages,
        temperature: finalOptions.temperature || 0.7,
        max_tokens: finalOptions.maxTokens || 2048,
        stream: true // STREAMING REAL
      };

      // Add PDF plugin configuration if PDFs are present
      if (hasPDFs && activeEndpoint.name.toLowerCase().includes('openrouter')) {
        streamData.plugins = [
          {
            id: 'file-parser',
            pdf: {
              engine: 'mistral-ocr' // Use mistral-ocr as requested
            }
          }
        ];
        console.log('PDF plugin configured for OpenRouter');
      }

      console.log('Fazendo requisição de streaming real:', {
        url: apiUrl,
        model: streamData.model,
        stream: streamData.stream,
        messageCount: streamData.messages.length,
        webSearchEnabled: finalOptions.webSearchEnabled,
        hasPDFs: hasPDFs,
        hasPlugins: !!streamData.plugins
      });

      // Fazer requisição de streaming real
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(streamData),
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erro na API do OpenRouter: ${response.status} - ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Não foi possível obter reader do stream');
      }

      const decoder = new TextDecoder();
      let currentContent = '';
      let buffer = '';

      console.log('Iniciando leitura do stream real...');

      try {
        while (true) {
          // Verificar cancelamento
          if (abortControllerRef.current?.signal.aborted) {
            const cancelTime = Date.now();
            const partialProcessingTime = cancelTime - startTime;
            console.log('Streaming cancelado pelo usuário. Conteúdo atual:', currentStreamContent);
            reader.releaseLock();
            return {
              success: false,
              response: currentStreamContent,
              metadata: {
                usedCoT: false,
                confidence: 0,
                processingTime: partialProcessingTime,
                model: model || finalOptions.model || 'google/gemini-2.5-flash',
                usage: usageData || {
                  prompt_tokens: 0,
                  completion_tokens: 0,
                  total_tokens: 0,
                  cost: 0
                },
                finishReason: 'cancelled'
              },
              wasCancelled: true
            };
          }

          const { done, value } = await reader.read();
          if (done) {
            console.log('Stream finalizado');
            break;
          }

          // Append new chunk to buffer (seguindo documentação OpenRouter)
          buffer += decoder.decode(value, { stream: true });

          // Process complete lines from buffer
          while (true) {
            const lineEnd = buffer.indexOf('\n');
            if (lineEnd === -1) break;

            const line = buffer.slice(0, lineEnd).trim();
            buffer = buffer.slice(lineEnd + 1);

            // Ignorar comentários SSE (: OPENROUTER PROCESSING)
            if (line.startsWith(':')) {
              console.log('Comentário SSE ignorado:', line);
              continue;
            }

            if (line.startsWith('data: ')) {
              const data = line.slice(6);

              if (data === '[DONE]') {
                console.log('Recebido [DONE] do OpenRouter');
                break;
              }

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content;

                if (content) {
                  currentContent += content;
                  currentStreamContent += content;

                  // Atualizar UI em tempo real
                  setStreamingContent(currentContent);

                  console.log('Chunk recebido:', content);
                }

                // Capturar informações de uso e modelo
                if (parsed.choices?.[0]?.finish_reason) {
                  finishReason = parsed.choices[0].finish_reason;
                  console.log('Finish reason:', finishReason);
                }

                if (parsed.model) {
                  model = parsed.model;
                  console.log('Modelo usado:', model);
                }

                // Capturar dados de uso (tokens, custo, etc.)
                if (parsed.usage) {
                  usageData = parsed.usage;
                  console.log('Dados de uso capturados:', usageData);
                }
              } catch (parseError) {
                // Ignorar JSON inválido (seguindo documentação)
                console.log('JSON inválido ignorado:', data);
                continue;
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      // Finalizar streaming
      setIsStreaming(false);
      setLoading(false);

      console.log('Streaming real concluído. Conteúdo final:', currentContent);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      console.log('Streaming concluído. Estatísticas:', {
        processingTime,
        usageData,
        model,
        finishReason,
        contentLength: currentStreamContent.length
      });

      // Calcular custo se não vier da API
      let calculatedCost = 0;
      if (usageData && (!usageData.cost || usageData.cost === 0)) {
        const modelId = model || finalOptions.model || 'google/gemini-2.5-flash';
        calculatedCost = await calculateCost(
          modelId,
          usageData.prompt_tokens || 0,
          usageData.completion_tokens || 0
        );
        console.log('Custo calculado:', calculatedCost);
      }

      return {
        success: true,
        response: currentStreamContent,
        metadata: {
          usedCoT: false,
          confidence: 0.8,
          processingTime,
          model: model || finalOptions.model || 'google/gemini-2.5-flash',
          usage: usageData ? {
            prompt_tokens: usageData.prompt_tokens || 0,
            completion_tokens: usageData.completion_tokens || 0,
            total_tokens: usageData.total_tokens || 0,
            cost: usageData.cost || calculatedCost
          } : {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0,
            cost: 0
          }
        },
        wasCancelled: false
      };

    } catch (error) {
      console.error('Erro no streaming:', error);

      // Verificar se foi cancelamento (seguindo documentação OpenRouter)
      if (error instanceof Error && error.name === 'AbortError') {
        const cancelTime = Date.now();
        const partialProcessingTime = cancelTime - startTime;
        console.log('Stream cancelado pelo usuário via AbortError. Conteúdo:', currentStreamContent);
        setIsStreaming(false);
        setLoading(false);
        return {
          success: false,
          response: currentStreamContent,
          metadata: {
            usedCoT: false,
            confidence: 0,
            processingTime: partialProcessingTime,
            model: model || finalOptions.model || 'google/gemini-2.5-flash',
            usage: usageData || {
              prompt_tokens: 0,
              completion_tokens: 0,
              total_tokens: 0,
              cost: 0
            },
            finishReason: 'cancelled'
          },
          wasCancelled: true
        };
      }

      setError(error instanceof Error ? error.message : 'Erro desconhecido');
      setIsStreaming(false);
      setLoading(false);

      return {
        success: false,
        wasCancelled: false
      };
    }
  }, [user, options, cancelStreaming]);

  return {
    generateStreamingResponse,
    cancelStreaming,
    loading,
    error,
    streamingContent,
    isStreaming,
    clearError: () => setError(null),
  };
};
