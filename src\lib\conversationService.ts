import { db } from './firebase';
import {
  collection,
  doc,
  addDoc,
  getDocs,
  query,
  orderBy,
  serverTimestamp,
  Timestamp,
  updateDoc,
  getDoc,
  deleteDoc,
  writeBatch,
  collectionGroup
} from 'firebase/firestore';
import { chatStorageService } from './chatStorageService';
import { ChatData } from './types/chat';
import { moveConversationToFolder } from './chatFolderService';
import { attachmentService } from './attachmentService';
import { storage } from './firebase';
import { ref, listAll, deleteObject } from 'firebase/storage';

// Cache para evitar requisições desnecessárias
interface ConversationCache {
  data: Conversation[];
  timestamp: number;
  userId: string;
}

let conversationCache: ConversationCache | null = null;
const CACHE_DURATION = 30000; // 30 segundos

export interface Conversation {
  id: string;
  userId: string;
  name: string;
  createdAt: any;
  systemPrompt?: string;
  context?: string;
  temperature?: number;
  repetitionPenalty?: number;
  frequencyPenalty?: number;
  maxTokens?: number;
  ultimaMensagem?: string;
  ultimaMensagemEm?: any;
  isFixed?: boolean;
  lastUsedService?: string;
  lastUsedModel?: string;
  latexInstructions?: boolean;
  folderId?: string; // ID da pasta onde o chat está organizado
  password?: string; // senha para proteger o chat
}

export interface ConversationInput {
  name: string;
  systemPrompt?: string;
  context?: string;
  temperature?: number;
  repetitionPenalty?: number;
  frequencyPenalty?: number;
  maxTokens?: number;
  isFixed?: boolean;
  latexInstructions?: boolean;
  folderId?: string;
  password?: string;
}

// Criar nova conversa para um usuário
export const createConversation = async (
  userId: string,
  name: string,
  config: Omit<ConversationInput, 'name'> = {}
): Promise<string> => {
  try {
    const now = Date.now();
    const conversationData = {
      userId,
      name,
      createdAt: serverTimestamp(),
      systemPrompt: config.systemPrompt || '',
      context: config.context || '',
      temperature: config.temperature !== undefined ? config.temperature : 1.0,
      repetitionPenalty: config.repetitionPenalty !== undefined ? config.repetitionPenalty : 2.0,
      frequencyPenalty: config.frequencyPenalty !== undefined ? config.frequencyPenalty : 2.0,
      maxTokens: config.maxTokens !== undefined ? config.maxTokens : 2048,
      isFixed: config.isFixed || false,
      latexInstructions: config.latexInstructions || false,
      folderId: config.folderId || null,
      password: config.password || null
    };

    // Criar documento no Firestore
    const userConversationsRef = collection(db, 'usuarios', userId, 'conversas');
    const docRef = await addDoc(userConversationsRef, conversationData);
    const chatId = docRef.id;

    // Criar arquivo JSON no Storage
    const chatDataForStorage: Omit<ChatData, 'messages'> = {
      id: chatId,
      name,
      context: config.context || '',
      system_prompt: config.systemPrompt || '',
      temperature: config.temperature !== undefined ? config.temperature : 1.0,
      maxTokens: config.maxTokens !== undefined ? config.maxTokens : 2048,
      frequency_penalty: config.frequencyPenalty !== undefined ? config.frequencyPenalty : 2.0,
      repetition_penalty: config.repetitionPenalty !== undefined ? config.repetitionPenalty : 2.0,
      createdAt: now,
      lastUpdatedAt: now,
      chatFolderId: undefined,
      lastUsedService: undefined,
      lastUsedModel: undefined,
      latexInstructions: config.latexInstructions || false,
      password: config.password || undefined
    };

    await chatStorageService.createChatFile(userId, chatId, chatDataForStorage);

    // Invalidar cache para forçar atualização
    invalidateConversationCache();

    return chatId;
  } catch (error) {
    console.error('Error creating conversation:', error);
    throw error;
  }
};

// Obter todas as conversas de um usuário com cache inteligente
export const getUserConversations = async (userId: string, useCache = true): Promise<Conversation[]> => {
  try {
    // Verificar cache se solicitado
    if (useCache && conversationCache &&
        conversationCache.userId === userId &&
        (Date.now() - conversationCache.timestamp) < CACHE_DURATION) {
      console.log('Using cached conversations');
      return conversationCache.data;
    }

    console.log('Fetching conversations from Firestore');
    const conversationsRef = collection(db, 'usuarios', userId, 'conversas');
    const q = query(conversationsRef, orderBy('createdAt', 'desc'));

    const querySnapshot = await getDocs(q);

    const conversations = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId,
        name: data.name || 'Sem título',
        createdAt: data.createdAt,
        systemPrompt: data.systemPrompt || '',
        context: data.context || '',
        temperature: data.temperature || 1.0,
        repetitionPenalty: data.repetitionPenalty || 2.0,
        frequencyPenalty: data.frequencyPenalty || 2.0,
        maxTokens: data.maxTokens || 2048,
        ultimaMensagem: data.ultimaMensagem || '',
        ultimaMensagemEm: data.ultimaMensagemEm,
        isFixed: data.isFixed || false,
        lastUsedService: data.lastUsedService,
        lastUsedModel: data.lastUsedModel,
        latexInstructions: data.latexInstructions || false,
        folderId: data.folderId || null,
        password: data.password || undefined
      };
    });

    // Atualizar cache
    conversationCache = {
      data: conversations,
      timestamp: Date.now(),
      userId
    };

    return conversations;
  } catch (error) {
    console.error('Erro ao obter conversas:', error);
    return [];
  }
};

// Invalidar cache quando necessário
export const invalidateConversationCache = () => {
  conversationCache = null;
  console.log('Conversation cache invalidated');
};

// Atualizar uma conversa existente
export const updateConversation = async (
  userId: string,
  conversationId: string,
  updates: Partial<ConversationInput>
) => {
  try {
    const conversationRef = doc(db, 'usuarios', userId, 'conversas', conversationId);

    // Filtrar valores undefined para evitar erro do Firebase
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await updateDoc(conversationRef, filteredUpdates);

    // Atualizar o arquivo JSON no Storage usando chatDataService (evita problemas de autenticação)
    if (Object.keys(filteredUpdates).length > 0) {
      try {
        // Importar chatDataService dinamicamente para evitar problemas de SSR
        const { chatDataService } = await import('./chatDataService');
        const chatData = await chatDataService.getChatData(userId, conversationId);

        if (chatData) {
          // Atualizar apenas os campos que foram modificados
          const updatedChatData: ChatData = {
            ...chatData,
            name: (filteredUpdates.name as string) || chatData.name,
            context: filteredUpdates.context !== undefined ? (filteredUpdates.context as string) : chatData.context,
            system_prompt: filteredUpdates.systemPrompt !== undefined ? (filteredUpdates.systemPrompt as string) : chatData.system_prompt,
            temperature: filteredUpdates.temperature !== undefined ? (filteredUpdates.temperature as number) : chatData.temperature,
            maxTokens: filteredUpdates.maxTokens !== undefined ? (filteredUpdates.maxTokens as number) : chatData.maxTokens,
            frequency_penalty: filteredUpdates.frequencyPenalty !== undefined ? (filteredUpdates.frequencyPenalty as number) : chatData.frequency_penalty,
            repetition_penalty: filteredUpdates.repetitionPenalty !== undefined ? (filteredUpdates.repetitionPenalty as number) : chatData.repetition_penalty,
            latexInstructions: filteredUpdates.latexInstructions !== undefined ? (filteredUpdates.latexInstructions as boolean) : chatData.latexInstructions,
            password: filteredUpdates.password !== undefined ? (filteredUpdates.password as string) : chatData.password,
            lastUpdatedAt: Date.now()
          };

          await chatDataService.updateChatData(userId, conversationId, updatedChatData);
          console.log(`Chat data updated in Storage for conversation: ${conversationId}`);
        }
      } catch (storageError) {
        console.warn('Failed to update Storage, but Firestore was updated successfully:', storageError);
        // Não falhar a operação se apenas o Storage falhar
      }
    }

    // Invalidar cache para forçar atualização
    invalidateConversationCache();
  } catch (error) {
    console.error('Erro ao atualizar conversa:', error);
    throw error;
  }
};

// Atualizar status de fixação de uma conversa
export const updateConversationFixedStatus = async (
  userId: string,
  conversationId: string,
  isFixed: boolean
): Promise<void> => {
  try {
    const conversationRef = doc(db, 'usuarios', userId, 'conversas', conversationId);
    await updateDoc(conversationRef, { isFixed });

    // Invalidar cache para forçar atualização
    invalidateConversationCache();

    console.log(`Conversation fixed status updated: ${conversationId} -> ${isFixed}`);
  } catch (error) {
    console.error('Erro ao atualizar status de fixação:', error);
    throw error;
  }
};

/**
 * Deleta todas as subcoleções de um chat no Firestore
 */
const deleteChatSubcollections = async (
  userId: string,
  conversationId: string
): Promise<void> => {
  try {
    const batch = writeBatch(db);
    let operationCount = 0;
    const MAX_BATCH_SIZE = 500;

    // Lista de subcoleções conhecidas
    const subcollections = ['estatisticas'];

    for (const subcollectionName of subcollections) {
      const subcollectionRef = collection(db, 'usuarios', userId, 'conversas', conversationId, subcollectionName);
      const subcollectionSnapshot = await getDocs(subcollectionRef);

      subcollectionSnapshot.docs.forEach(docSnapshot => {
        if (operationCount >= MAX_BATCH_SIZE) {
          return; // Parar se atingir o limite do batch
        }
        batch.delete(docSnapshot.ref);
        operationCount++;
      });
    }

    if (operationCount > 0) {
      await batch.commit();
      console.log(`Deleted ${operationCount} documents from subcollections for chat ${conversationId}`);
    }
  } catch (error) {
    console.error('Error deleting chat subcollections:', error);
    throw error;
  }
};

/**
 * Deleta completamente a pasta do chat no Storage
 */
const deleteChatStorageFolder = async (
  userId: string,
  conversationId: string
): Promise<void> => {
  try {
    const chatFolderRef = ref(storage, `usuarios/${userId}/conversas/${conversationId}`);

    // Listar todos os arquivos na pasta do chat
    const listResult = await listAll(chatFolderRef);

    // Deletar todos os arquivos
    const deletePromises = listResult.items.map(itemRef => deleteObject(itemRef));

    // Deletar todas as subpastas (anexos, etc.)
    const subfolderPromises = listResult.prefixes.map(async (folderRef) => {
      const subListResult = await listAll(folderRef);
      const subDeletePromises = subListResult.items.map(itemRef => deleteObject(itemRef));
      await Promise.all(subDeletePromises);
    });

    await Promise.all([...deletePromises, ...subfolderPromises]);

    console.log(`Storage folder deleted successfully: usuarios/${userId}/conversas/${conversationId}`);
  } catch (error) {
    console.error('Error deleting chat storage folder:', error);
    // Não lançar erro se a pasta não existir
    if ((error as any)?.code !== 'storage/object-not-found') {
      throw error;
    }
  }
};

/**
 * Remove favoritos órfãos que referenciam o chat deletado
 * Usa API para evitar problemas de CORS
 */
const cleanupOrphanedFavorites = async (
  userId: string,
  conversationId: string
): Promise<void> => {
  try {
    console.log(`🧹 Limpando favoritos órfãos para chat ${conversationId}...`);

    // Usar API para limpar favoritos órfãos (evita CORS)
    const response = await fetch('/api/favorites/cleanup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        chatId: conversationId
      }),
    });

    if (response.ok) {
      const result = await response.json();
      if (result.cleanedCount > 0) {
        console.log(`✅ Cleaned up ${result.cleanedCount} orphaned favorites for chat ${conversationId}`);
      } else {
        console.log(`ℹ️ No orphaned favorites found for chat ${conversationId}`);
      }
    } else {
      console.warn(`⚠️ Failed to cleanup favorites via API: ${response.status}`);
      // Tentar método direto como fallback
      await cleanupOrphanedFavoritesDirect(userId, conversationId);
    }
  } catch (error) {
    console.warn('⚠️ Error cleaning up orphaned favorites via API, trying direct method:', error);
    // Fallback para método direto
    await cleanupOrphanedFavoritesDirect(userId, conversationId);
  }
};

/**
 * Método direto para limpeza de favoritos (fallback)
 */
const cleanupOrphanedFavoritesDirect = async (
  userId: string,
  conversationId: string
): Promise<void> => {
  try {
    // Carregar favoritos do Storage diretamente
    const favoritesRef = ref(storage, `usuarios/${userId}/favoritos.json`);

    try {
      const { getBytes } = await import('firebase/storage');
      const bytes = await getBytes(favoritesRef);
      const jsonString = new TextDecoder().decode(bytes);
      const favorites = JSON.parse(jsonString);

      // Filtrar favoritos que não pertencem ao chat deletado
      const filteredFavorites = favorites.filter((fav: any) => fav.chatId !== conversationId);

      // Salvar favoritos filtrados de volta
      if (filteredFavorites.length !== favorites.length) {
        const { uploadBytes } = await import('firebase/storage');
        const updatedData = new TextEncoder().encode(JSON.stringify(filteredFavorites, null, 2));
        await uploadBytes(favoritesRef, updatedData);
        console.log(`✅ Direct cleanup: ${favorites.length - filteredFavorites.length} orphaned favorites removed`);
      }
    } catch (error) {
      // Se o arquivo de favoritos não existir, não é um erro
      if ((error as any)?.code === 'storage/object-not-found') {
        console.log('ℹ️ No favorites file found, skipping cleanup');
        return;
      }
      throw error;
    }
  } catch (error) {
    console.error('❌ Error in direct favorites cleanup:', error);
    // Não falhar a exclusão do chat por causa dos favoritos
    console.log('⚠️ Favorites cleanup failed, but chat deletion will continue');
  }
};

// Deletar uma conversa (Firestore + Storage + Anexos + Subcoleções + Favoritos)
export const deleteConversation = async (
  userId: string,
  conversationId: string
): Promise<void> => {
  try {
    console.log(`Starting complete deletion of chat ${conversationId}...`);

    // 1. Deletar todas as subcoleções do Firestore
    console.log('Deleting Firestore subcollections...');
    await deleteChatSubcollections(userId, conversationId);

    // 2. Deletar todos os anexos do chat
    console.log('Deleting chat attachments...');
    const attachmentResult = await attachmentService.deleteChatAttachments(userId, conversationId);

    if (attachmentResult.success) {
      console.log(`Successfully deleted ${attachmentResult.deletedCount} attachments`);
      if (attachmentResult.errors.length > 0) {
        console.warn('Some attachment deletions failed:', attachmentResult.errors);
      }
    } else {
      console.error('Failed to delete attachments:', attachmentResult.errors);
      // Continuar com a deleção do chat mesmo se houver erro nos anexos
    }

    // 3. Deletar documento principal do Firestore
    console.log('Deleting main Firestore document...');
    const conversationRef = doc(db, 'usuarios', userId, 'conversas', conversationId);
    await deleteDoc(conversationRef);

    // 4. Deletar completamente a pasta do Storage
    console.log('Deleting complete Storage folder...');
    await deleteChatStorageFolder(userId, conversationId);

    // 5. Limpar favoritos órfãos
    console.log('Cleaning up orphaned favorites...');
    await cleanupOrphanedFavorites(userId, conversationId);
    console.log('✅ Orphaned favorites cleanup completed');

    // 6. Invalidar cache para forçar atualização
    invalidateConversationCache();

    console.log(`✅ Conversation deleted completely: ${conversationId}`);
  } catch (error) {
    console.error('❌ Erro ao deletar conversa:', error);
    throw error;
  }
};

/**
 * Move uma conversa para uma pasta específica
 */
export const moveConversationToChatFolder = async (
  userId: string,
  conversationId: string,
  folderId: string | null
): Promise<void> => {
  try {
    // Usar a função do chatFolderService
    await moveConversationToFolder(userId, conversationId, folderId);

    // Invalidar cache para forçar atualização
    invalidateConversationCache();

    console.log(`Conversation ${conversationId} moved to folder ${folderId || 'none'}`);
  } catch (error) {
    console.error('Error moving conversation to folder:', error);
    throw error;
  }
};

/**
 * Obtém conversas organizadas por pastas
 */
export const getConversationsGroupedByFolder = async (
  userId: string,
  useCache = true
): Promise<{
  folderedConversations: { [folderId: string]: Conversation[] };
  unorganizedConversations: Conversation[];
}> => {
  try {
    const conversations = await getUserConversations(userId, useCache);

    const folderedConversations: { [folderId: string]: Conversation[] } = {};
    const unorganizedConversations: Conversation[] = [];

    conversations.forEach(conversation => {
      if (conversation.folderId) {
        if (!folderedConversations[conversation.folderId]) {
          folderedConversations[conversation.folderId] = [];
        }
        folderedConversations[conversation.folderId].push(conversation);
      } else {
        unorganizedConversations.push(conversation);
      }
    });

    return {
      folderedConversations,
      unorganizedConversations
    };
  } catch (error) {
    console.error('Error getting conversations grouped by folder:', error);
    throw error;
  }
};