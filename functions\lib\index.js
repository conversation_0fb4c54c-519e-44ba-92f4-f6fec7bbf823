import { on<PERSON><PERSON>, HttpsError } from "firebase-functions/v2/https";
import { logger } from "firebase-functions";
import { initializeApp } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";
import { AIService } from "./services/aiService.js";
// Inicializar Firebase Admin
initializeApp();
// Inicializar serviço de IA
const aiService = new AIService();
/**
 * Função para encontrar endpoint apropriado baseado no modelo
 * @param {any} db - Instância do Firestore
 * @param {string} userId - ID do usuário
 * @param {string} model - Nome do modelo
 * @return {Promise<{doc: any, data: EndpointData} | null>} Endpoint encontrado
 */
async function findEndpointForModel(db, userId, model) {
    // Mapear modelos para provedores
    const modelToProvider = {
        "deepseek-chat": "deepseek",
        "deepseek-reasoner": "deepseek",
        "gpt-4": "openai",
        "gpt-3.5-turbo": "openai",
        "claude-3-opus": "anthropic",
        "claude-3-sonnet": "anthropic",
        "google/gemini-2.5-flash": "openrouter",
    };
    const provider = modelToProvider[model];
    if (!provider) {
        return null;
    }
    // Buscar endpoints ativos que correspondem ao provedor
    const endpointsSnapshot = await db
        .collection("usuarios")
        .doc(userId)
        .collection("endpoints")
        .where("isActive", "==", true)
        .get();
    for (const doc of endpointsSnapshot.docs) {
        const data = doc.data();
        const endpointName = data.name.toLowerCase();
        if ((provider === "deepseek" && endpointName.includes("deepseek")) ||
            (provider === "openai" && (endpointName.includes("openai") ||
                endpointName.includes("openrouter"))) ||
            (provider === "anthropic" && (endpointName.includes("anthropic") ||
                endpointName.includes("claude")))) {
            return { doc, data };
        }
    }
    return null;
}
// Função para testar endpoint de IA
export const testAIEndpoint = onCall(async (request) => {
    var _a;
    try {
        // Verificar autenticação
        if (!request.auth) {
            throw new HttpsError("unauthenticated", "Usuário não autenticado");
        }
        const { endpoint, apiKey, model } = request.data;
        // Validar dados de entrada
        if (!endpoint || !apiKey) {
            throw new HttpsError("invalid-argument", "Endpoint e API key são obrigatórios");
        }
        logger.info(`Testando endpoint: ${endpoint}`, { userId: request.auth.uid });
        // Fazer requisição de teste para o endpoint
        const testMessage = {
            model: model || "gpt-3.5-turbo",
            messages: [
                {
                    role: "user",
                    content: "Teste de conectividade. Responda apenas 'OK'.",
                },
            ],
            maxTokens: 10,
            temperature: 0,
        };
        const response = await fetch(`${endpoint}/chat/completions`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${apiKey}`,
                "User-Agent": "RafthorIA/1.0",
            },
            body: JSON.stringify(testMessage),
        });
        if (!response.ok) {
            const errorText = await response.text();
            logger.error(`Erro no teste do endpoint: ${response.status}`, {
                endpoint,
                error: errorText,
                userId: request.auth.uid,
            });
            throw new HttpsError("internal", `Erro no endpoint: ${response.status} - ${errorText}`);
        }
        const result = await response.json();
        logger.info("Teste de endpoint bem-sucedido", {
            endpoint,
            userId: request.auth.uid,
            model: result.model || model,
        });
        return {
            success: true,
            message: "Endpoint funcionando corretamente",
            model: result.model || model,
            usage: result.usage || null,
        };
    }
    catch (error) {
        logger.error("Erro ao testar endpoint", { error, userId: (_a = request.auth) === null || _a === void 0 ? void 0 : _a.uid });
        if (error instanceof HttpsError) {
            throw error;
        }
        throw new HttpsError("internal", `Erro interno: ${error}`);
    }
});
// Função principal de chat/completions com IA integrada
export const chatCompletion = onCall(async (request) => {
    var _a, _b;
    try {
        // Verificar autenticação
        if (!request.auth) {
            throw new HttpsError("unauthenticated", "Usuário não autenticado");
        }
        const userId = request.auth.uid;
        const chatData = request.data;
        // Validar dados de entrada
        if (!chatData.messages || !Array.isArray(chatData.messages) ||
            chatData.messages.length === 0) {
            throw new HttpsError("invalid-argument", "Messages são obrigatórias");
        }
        logger.info("Processando requisição de chat com IA", {
            userId,
            messageCount: chatData.messages.length,
            model: chatData.model,
            hasAttachments: chatData.messages.some((msg) => Array.isArray(msg.content) && msg.content.length > 0),
            hasMemories: !!chatData.memories,
            memoriesLength: ((_a = chatData.memories) === null || _a === void 0 ? void 0 : _a.length) || 0,
        });
        if (chatData.memories) {
            logger.info("🧠 Memórias recebidas:", {
                memories: chatData.memories,
                length: chatData.memories.length,
            });
        }
        // Debug: Log das mensagens para verificar anexos
        chatData.messages.forEach((msg, index) => {
            var _a;
            if (Array.isArray(msg.content)) {
                logger.info(`Mensagem ${index} tem conteúdo multimodal`, {
                    userId,
                    messageIndex: index,
                    contentTypes: msg.content.map((c) => c.type),
                    contentCount: msg.content.length,
                    fullContent: msg.content, // Log completo do conteúdo
                });
            }
            else {
                logger.info(`Mensagem ${index} tem conteúdo string`, {
                    userId,
                    messageIndex: index,
                    contentLength: ((_a = msg.content) === null || _a === void 0 ? void 0 : _a.length) || 0,
                });
            }
        });
        // Buscar endpoint apropriado baseado no modelo
        const db = getFirestore();
        let endpointDoc = null;
        let endpointData = null;
        // Primeiro, tentar encontrar um endpoint específico baseado no modelo
        if (chatData.model) {
            const endpointForModel = await findEndpointForModel(db, userId, chatData.model);
            if (endpointForModel) {
                endpointDoc = endpointForModel.doc;
                endpointData = endpointForModel.data;
            }
        }
        // Se não encontrou endpoint específico, buscar qualquer endpoint ativo
        if (!endpointDoc) {
            const userDoc = await db
                .collection("usuarios")
                .doc(userId)
                .collection("endpoints")
                .where("isActive", "==", true)
                .limit(1)
                .get();
            if (userDoc.empty) {
                throw new HttpsError("failed-precondition", "Nenhum endpoint ativo configurado");
            }
            endpointDoc = userDoc.docs[0];
            endpointData = endpointDoc.data();
        }
        // Verificar se encontrou um endpoint
        if (!endpointData) {
            throw new HttpsError("failed-precondition", "Nenhum endpoint configurado para este modelo");
        }
        // Adicionar informações do endpoint
        const endpointInfo = {
            endpoint: endpointData.endpoint,
            apiKey: endpointData.apiKey,
            name: endpointData.name, // Nome do endpoint (OpenAI, DeepSeek, etc.)
        };
        // Preparar requisição para o serviço de IA
        const aiRequest = {
            messages: chatData.messages,
            model: chatData.model ||
                endpointData.defaultModel ||
                "google/gemini-2.5-flash",
            temperature: chatData.temperature,
            maxTokens: chatData.maxTokens,
            userId,
            endpointData: endpointInfo,
            latexInstructions: chatData.latexInstructions || false,
            webSearchEnabled: chatData.webSearchEnabled || false,
            memories: chatData.memories || "",
            systemPrompt: chatData.systemPrompt || "",
            context: chatData.context || "",
        };
        logger.info("Processando com serviço de IA", {
            endpoint: endpointInfo.name,
            model: aiRequest.model,
            userId,
        });
        // Processar com o serviço de IA (inclui CoT)
        const aiResponse = await aiService.processAIRequest(aiRequest);
        logger.info("Chat completion com IA bem-sucedido", {
            userId,
            model: aiResponse.model,
            usage: aiResponse.usage,
            usedCoT: aiResponse.usedCoT,
            confidence: aiResponse.confidence,
            processingTime: aiResponse.processingTime,
        });
        // Retornar no formato esperado pelo frontend
        return {
            choices: [{
                    message: {
                        role: "assistant",
                        content: aiResponse.content,
                    },
                    finish_reason: "stop",
                }],
            model: aiResponse.model,
            usage: aiResponse.usage,
            // Metadados adicionais do RafthorIA
            rafthoria_metadata: {
                usedCoT: aiResponse.usedCoT,
                confidence: aiResponse.confidence,
                processingTime: aiResponse.processingTime,
            },
        };
    }
    catch (error) {
        logger.error("Erro no chat completion com IA", {
            error,
            userId: (_b = request.auth) === null || _b === void 0 ? void 0 : _b.uid,
        });
        if (error instanceof HttpsError) {
            throw error;
        }
        throw new HttpsError("internal", `Erro interno: ${error}`);
    }
});
/**
 * Função para chat completion com streaming
 */
export const chatCompletionStream = onCall(async (request) => {
    var _a;
    try {
        // Verificar autenticação
        if (!request.auth) {
            throw new HttpsError("unauthenticated", "Usuário não autenticado");
        }
        const userId = request.auth.uid;
        const { messages, model = "google/gemini-2.5-flash", temperature = 0.7, maxTokens = 2048, latexInstructions = false, webSearchEnabled = false, memories = [], streaming = true, streamId, systemPrompt = "", context = "", } = request.data;
        // Validar dados obrigatórios
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            throw new HttpsError("invalid-argument", "Messages são obrigatórias");
        }
        logger.info("Iniciando chat completion com streaming", {
            userId,
            model,
            messageCount: messages.length,
            streaming,
            streamId,
        });
        // Buscar endpoint apropriado
        const db = getFirestore();
        const endpointResult = await findEndpointForModel(db, userId, model);
        if (!endpointResult) {
            throw new HttpsError("failed-precondition", `Nenhum endpoint configurado para o modelo: ${model}`);
        }
        const { data: endpointData } = endpointResult;
        // Preparar requisição para o serviço de IA
        const aiRequest = {
            userId,
            messages,
            model,
            temperature,
            maxTokens,
            endpointData,
            latexInstructions,
            webSearchEnabled,
            memories,
            systemPrompt,
            context,
        };
        // Chamar serviço de IA com streaming
        const response = await aiService.generateResponseWithStreaming(aiRequest, endpointData);
        logger.info("Resposta do streaming gerada com sucesso", {
            userId,
            model,
            hasResponse: !!response.response,
            isStreaming: response.isStreaming,
        });
        return response;
    }
    catch (error) {
        logger.error("Erro no chat completion com streaming", {
            error,
            userId: (_a = request.auth) === null || _a === void 0 ? void 0 : _a.uid,
        });
        if (error instanceof HttpsError) {
            throw error;
        }
        throw new HttpsError("internal", `Erro interno: ${error}`);
    }
});
//# sourceMappingURL=index.js.map