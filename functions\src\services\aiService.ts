import {logger} from "firebase-functions";
import aiConfig from "../config/ai-config.json" with { type: "json" };
import {LATEX_INSTRUCTIONS_PROMPT} from "../prompts/latexInstructions.js";

// Interfaces
interface MessageContent {
  type: "text" | "image_url" | "file";
  text?: string;
  image_url?: {
    url: string;
  };
  file?: {
    filename: string;
    file_data: string;
  };
}

interface Message {
  role: "system" | "user" | "assistant";
  content: string | MessageContent[];
}

interface AIRequest {
  messages: Message[];
  model?: string;
  temperature?: number;
  maxTokens?: number;
  userId: string;
  endpointData: {
    endpoint: string;
    apiKey: string;
    name: string;
  };
  latexInstructions?: boolean;
  webSearchEnabled?: boolean;
  memories?: string;
  systemPrompt?: string;
  context?: string;
}

interface AIResponse {
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  processingTime: number;
  confidence?: number;
  usedCoT: boolean;
}

/**
 * Serviço de IA com suporte a Chain of Thought
 */
export class AIService {
  /**
   * Obtém configuração do modelo
   * @param {string} model - Nome do modelo
   * @return {any} Configuração do modelo
   */
  private getModelConfig(model: string): any {
    for (const provider of Object.values(aiConfig.modelConfigs)) {
      if (typeof provider === "object" && provider !== null &&
          model in provider) {
        return (provider as any)[model];
      }
    }
    return null;
  }

  /**
   * Obtém o provedor baseado no modelo
   * @param {string} model - Nome do modelo
   * @return {string} Nome do provedor
   */
  private getProviderFromModel(model: string): string {
    for (const [providerName, provider] of
      Object.entries(aiConfig.modelConfigs)) {
      if (typeof provider === "object" && provider !== null &&
          model in provider) {
        return providerName;
      }
    }
    return "openrouter"; // Default para OpenRouter
  }

  /**
   * Constrói o prompt do sistema baseado no contexto
   * @param {boolean} latexInstructions - Se deve incluir instruções LaTeX
   * @param {string} memories - Memórias formatadas do usuário
   * @param {string} systemPrompt - System prompt da conversa
   * @param {string} context - Contexto da conversa
   * @return {Message} Prompt do sistema
   */
  private buildSystemPrompt(
    latexInstructions = false,
    memories = "",
    systemPrompt = "",
    context = "",
  ): Message {
    // Construir o conteúdo do prompt
    let promptContent = "";

    // Adicionar system prompt da conversa se existir
    if (systemPrompt) {
      console.log("📝 Adicionando system prompt da conversa:", systemPrompt);
      promptContent += systemPrompt;
    }

    // Adicionar contexto da conversa se existir
    if (context) {
      console.log("📄 Adicionando contexto da conversa:", context);
      promptContent += (promptContent ? "\n\n" : "") + context;
    }

    // Adicionar memórias se existirem
    if (memories) {
      console.log("🧠 Adicionando memórias ao prompt:", memories);
      promptContent += (promptContent ? "\n\n" : "") + memories;
    } else {
      console.log("🧠 Nenhuma memória encontrada para adicionar ao prompt");
    }

    // Se instruções LaTeX estão ativadas, adiciona ao prompt
    if (latexInstructions) {
      promptContent += (promptContent ? "\n\n" : "") +
        LATEX_INSTRUCTIONS_PROMPT;
    }

    console.log("📝 System prompt final:", promptContent);

    return {
      role: "system",
      content: promptContent,
    };
  }

  /**
   * Processa mensagens normalmente
   * @param {AIRequest} request - Dados da requisição
   * @return {Promise<AIResponse>} Resposta da IA
   */
  private async processNormal(
    request: AIRequest,
  ): Promise<AIResponse> {
    const startTime = Date.now();

    const response = await this.callAIEndpoint(request);
    const processingTime = Date.now() - startTime;

    return {
      ...response,
      processingTime,
      usedCoT: false,
      confidence: this.calculateConfidence(response.content, false),
    };
  }

  /**
   * Calcula nível de confiança da resposta
   * @param {string} content - Conteúdo da resposta
   * @param {boolean} usedCoT - Se foi usado CoT
   * @return {number} Nível de confiança
   */
  private calculateConfidence(content: string, usedCoT: boolean): number {
    let confidence = 70; // Base confidence

    // CoT geralmente aumenta a confiança
    if (usedCoT) confidence += 15;

    // Respostas mais longas e estruturadas tendem a ser mais confiáveis
    if (content.length > 500) confidence += 10;
    if (content.includes("**") || content.includes("###")) confidence += 5;

    // Presença de exemplos ou explicações detalhadas
    if (content.includes("exemplo") ||
        content.includes("por exemplo")) confidence += 5;

    // Limitações ou incertezas reduzem a confiança
    if (content.includes("não tenho certeza") ||
        content.includes("pode ser")) confidence -= 10;

    return Math.min(95, Math.max(30, confidence));
  }

  /**
   * Chama o endpoint de IA
   * @param {AIRequest} request - Dados da requisição
   * @return {Promise} Resposta do endpoint
   */
  private async callAIEndpoint(request: AIRequest): Promise<Omit<AIResponse,
    "processingTime" | "usedCoT" | "confidence">> {
    const {
      endpointData,
      messages,
      model,
      temperature,
      maxTokens,
      webSearchEnabled,
    } = request;

    // Log para debug
    logger.info("Debug endpoint info", {
      endpointName: endpointData.name,
      endpointUrl: endpointData.endpoint,
      model: model,
    });

    // Determina o provedor baseado no endpoint
    const provider = this.determineProvider(endpointData.name);

    // Log para debug
    logger.info("Debug provider detection", {
      endpointName: endpointData.name,
      detectedProvider: provider,
    });

    const endpoints = aiConfig.endpoints as any;
    const endpointConfig = endpoints[provider];

    if (!endpointConfig) {
      throw new Error(`Provedor não suportado: ${provider}`);
    }

    const modelConfig = this.getModelConfig(model || "google/gemini-2.5-flash");

    // Determine the final model name, adding :online for OpenRouter web search
    let finalModel = model || "google/gemini-2.5-flash";
    if (webSearchEnabled && provider === "openrouter") {
      // Add :online suffix for OpenRouter web search
      finalModel = finalModel.includes(":online") ?
        finalModel : `${finalModel}:online`;

      logger.info("Web search enabled for OpenRouter", {
        originalModel: model,
        finalModel: finalModel,
        userId: request.userId,
      });
    }

    // Check if messages contain PDFs to add plugin configuration
    const hasPDFs = messages.some((msg) =>
      Array.isArray(msg.content) &&
      msg.content.some((content) => content.type === "file")
    );

    const requestData: any = {
      model: finalModel,
      messages,
      temperature: temperature ?? modelConfig?.temperature ?? 0.7,
      max_tokens: maxTokens ?? modelConfig?.maxTokens ?? 2048,
      top_p: modelConfig?.topP ?? 1.0,
      frequency_penalty: modelConfig?.frequencyPenalty ?? 0.0,
      presence_penalty: modelConfig?.presencePenalty ?? 0.0,
      // Enable usage accounting for OpenRouter
      ...(provider === "openrouter" && {
        usage: {
          include: true,
        },
      }),
    };

    // Add PDF plugin configuration if PDFs are present
    if (hasPDFs && provider === "openrouter") {
      requestData.plugins = [
        {
          id: "file-parser",
          pdf: {
            engine: "mistral-ocr", // Use mistral-ocr as requested
          },
        },
      ];
    }

    // Web search is handled by the :online suffix in the model name
    // No additional configuration needed for OpenRouter :online models

    // Ajusta formato para Anthropic se necessário
    if (provider === "anthropic") {
      return this.callAnthropicAPI(endpointData, requestData);
    }

    // Formato padrão OpenAI-compatible
    const response = await fetch(
      `${endpointData.endpoint}${endpointConfig.chatPath}`, {
        method: "POST",
        headers: {
          ...endpointConfig.headers,
          "Authorization": `Bearer ${endpointData.apiKey}`,
        },
        body: JSON.stringify(requestData),
      });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`Erro na requisição de IA: ${response.status}`, {
        endpoint: endpointData.endpoint,
        error: errorText,
        userId: request.userId,
      });
      throw new Error(`Erro no endpoint: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    return {
      content: result.choices[0].message.content,
      model: result.model,
      usage: result.usage,
    };
  }

  /**
   * Chama API da Anthropic (formato diferente)
   * @param {any} endpointData - Dados do endpoint
   * @param {any} requestData - Dados da requisição
   * @return {Promise} Resposta da API
   */
  private async callAnthropicAPI(endpointData: any,
    requestData: any): Promise<Omit<AIResponse,
    "processingTime" | "usedCoT" | "confidence">> {
    // Converte formato OpenAI para Anthropic
    const systemMessage = requestData.messages.find(
      (m: Message) => m.role === "system");
    const userMessages = requestData.messages.filter(
      (m: Message) => m.role !== "system");

    const anthropicRequest = {
      model: requestData.model,
      max_tokens: requestData.max_tokens,
      temperature: requestData.temperature,
      system: systemMessage?.content,
      messages: userMessages,
    };

    const response = await fetch(`${endpointData.endpoint}/v1/messages`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": endpointData.apiKey,
        "anthropic-version": "2023-06-01",
      },
      body: JSON.stringify(anthropicRequest),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Erro Anthropic: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    return {
      content: result.content[0].text,
      model: result.model,
      usage: result.usage,
    };
  }

  /**
   * Determina o provedor baseado no nome do endpoint
   * @param {string} endpointName - Nome do endpoint
   * @return {string} Nome do provedor
   */
  private determineProvider(endpointName: string): string {
    const name = endpointName.toLowerCase();
    if (name.includes("openai")) return "openai";
    if (name.includes("anthropic") ||
        name.includes("claude")) return "anthropic";
    if (name.includes("deepseek")) return "deepseek";
    if (name.includes("openrouter")) return "openrouter";
    return "openai"; // Default
  }

  /**
   * Método principal para processar requisições de IA
   * @param {AIRequest} request - Dados da requisição
   * @return {Promise<AIResponse>} Resposta processada
   */
  async processAIRequest(request: AIRequest): Promise<AIResponse> {
    try {
      const userMessage = request.messages[request.messages.length - 1]?.
        content || "";
      // CoT desabilitado - sempre usar processamento normal
      const useCoT = false;

      // Adiciona prompt do sistema apropriado
      const systemPrompt = this.buildSystemPrompt(
        request.latexInstructions,
        request.memories || "",
        request.systemPrompt || "",
        request.context || "",
      );
      const messagesWithSystem = [
        systemPrompt,
        ...request.messages.filter((m) => m.role !== "system"),
      ];

      const requestWithSystem = {
        ...request,
        messages: messagesWithSystem,
      };

      logger.info("Processando requisição de IA", {
        userId: request.userId,
        model: request.model,
        useCoT,
        messageLength: userMessage.length,
      });

      // Sempre processa normalmente (CoT desabilitado)
      return await this.processNormal(requestWithSystem);
    } catch (error) {
      logger.error("Erro no processamento de IA", {
        error, userId: request.userId,
      });
      throw error;
    }
  }

  /**
   * Gera resposta com streaming
   * @param {AIRequest} request - Dados da requisição
   * @param {any} endpointData - Dados do endpoint
   * @return {Promise} Promise com a resposta
   */
  async generateResponseWithStreaming(
    request: AIRequest,
    endpointData: any,
  ): Promise<{
    response?: string;
    isStreaming: boolean;
    streamUrl?: string;
    metadata?: any;
  }> {
    const startTime = Date.now();

    try {
      logger.info("Iniciando geração com streaming", {
        model: request.model,
        userId: request.userId,
        provider: this.getProviderFromModel(
          request.model || "google/gemini-2.5-flash"),
      });

      // Preparar mensagens com sistema
      const systemPrompt = this.buildSystemPrompt(
        request.latexInstructions,
        request.memories || "",
        request.systemPrompt || "",
        request.context || "",
      );
      const messagesWithSystem = [
        systemPrompt,
        ...request.messages.filter((m) => m.role !== "system"),
      ];

      const requestWithSystem = {
        ...request,
        messages: messagesWithSystem,
      };

      const provider = this.getProviderFromModel(
        request.model || "google/gemini-2.5-flash");

      // Verificar se o provedor suporta streaming
      if (provider === "openrouter") {
        return await this.generateOpenRouterStreaming(
          messagesWithSystem,
          requestWithSystem,
          endpointData,
          startTime,
        );
      } else {
        // Fallback para outros provedores:
        // gerar resposta completa e simular streaming
        const response = await this.processNormal(requestWithSystem);
        return {
          response: response.content,
          isStreaming: false,
          metadata: {
            usedCoT: response.usedCoT,
            confidence: response.confidence || 0.8,
            processingTime: response.processingTime,
            model: response.model,
            usage: response.usage,
          },
        };
      }
    } catch (error) {
      logger.error("Erro na geração com streaming", {
        error,
        userId: request.userId,
      });
      throw error;
    }
  }

  /**
   * Gera streaming específico para OpenRouter
   * @param {any[]} messages - Array de mensagens
   * @param {AIRequest} request - Dados da requisição
   * @param {any} endpointData - Dados do endpoint
   * @param {number} startTime - Timestamp de início
   * @return {Promise} Promise com a resposta
   */
  private async generateOpenRouterStreaming(
    messages: any[],
    request: AIRequest,
    endpointData: any,
    startTime: number,
  ): Promise<{
    response?: string;
    isStreaming: boolean;
    streamUrl?: string;
    metadata?: any;
  }> {
    const endpoints = aiConfig.endpoints as any;
    const endpointConfig = endpoints.openrouter;

    if (!endpointConfig) {
      throw new Error("Configuração do OpenRouter não encontrada");
    }

    const modelConfig = this.getModelConfig(
      request.model || "google/gemini-2.5-flash");

    // Determinar modelo final
    let finalModel = request.model || "google/gemini-2.5-flash";
    if (request.webSearchEnabled) {
      finalModel = finalModel.includes(":online") ?
        finalModel : `${finalModel}:online`;
    }

    // Preparar dados da requisição
    const requestData = {
      model: finalModel,
      messages,
      temperature: request.temperature || modelConfig.temperature,
      max_tokens: request.maxTokens || modelConfig.maxTokens,
      stream: true, // Habilitar streaming
    };

    logger.info("Fazendo requisição de streaming para OpenRouter", {
      model: finalModel,
      userId: request.userId,
      endpoint: endpointData.endpoint,
    });

    try {
      // Fazer requisição de streaming para OpenRouter
      const response = await fetch(
        `${endpointData.endpoint}${endpointConfig.chatPath}`,
        {
          method: "POST",
          headers: {
            ...endpointConfig.headers,
            "Authorization": `Bearer ${endpointData.apiKey}`,
          },
          body: JSON.stringify(requestData),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        logger.error("Erro na requisição de streaming", {
          status: response.status,
          error: errorText,
          userId: request.userId,
        });
        throw new Error(`Erro HTTP ${response.status}: ${errorText}`);
      }

      // Para streaming real, retornaríamos uma URL ou stream
      // Por simplicidade, vamos processar a resposta e simular streaming
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("Não foi possível obter reader do stream");
      }

      let fullContent = "";
      const decoder = new TextDecoder();

      try {
        while (true) {
          const {done, value} = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = line.slice(6);
              if (data === "[DONE]") continue;

              try {
                const parsed = JSON.parse(data);
                if (parsed.choices?.[0]?.delta?.content) {
                  fullContent += parsed.choices[0].delta.content;
                }
              } catch (parseError) {
                // Ignorar erros de parsing de chunks individuais
                continue;
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      const processingTime = Date.now() - startTime;

      return {
        response: fullContent,
        isStreaming: false, // Retornamos false pois já processamos tudo
        metadata: {
          usedCoT: false,
          confidence: 0.8,
          processingTime,
          model: finalModel,
          usage: {
            promptTokens: 0, // OpenRouter não fornece esses dados no streaming
            completionTokens: 0,
            totalTokens: 0,
          },
        },
      };
    } catch (error) {
      logger.error("Erro no streaming do OpenRouter", {
        error,
        userId: request.userId,
      });
      throw error;
    }
  }
}
