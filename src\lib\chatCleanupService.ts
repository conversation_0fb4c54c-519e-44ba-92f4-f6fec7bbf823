import { db, storage } from './firebase';
import {
  collection,
  doc,
  getDocs,
  query,
  deleteDoc,
  writeBatch,
  getDoc
} from 'firebase/firestore';
import { ref, listAll, deleteObject, getBytes, uploadBytes } from 'firebase/storage';

interface OrphanedData {
  subcollections: string[];
  storageFiles: string[];
  favorites: number;
}

interface CleanupResult {
  chatId: string;
  orphanedData: OrphanedData;
  cleanupSuccess: boolean;
  errors: string[];
}

interface AuditResult {
  totalChatsChecked: number;
  orphanedChatsFound: string[];
  orphanedSubcollections: number;
  orphanedStorageFiles: number;
  orphanedFavorites: number;
}

class ChatCleanupServiceImpl {
  /**
   * Audita todos os chats de um usuário para encontrar dados órfãos
   */
  async auditUserChats(userId: string): Promise<AuditResult> {
    try {
      console.log(`Starting audit for user ${userId}...`);

      const result: AuditResult = {
        totalChatsChecked: 0,
        orphanedChatsFound: [],
        orphanedSubcollections: 0,
        orphanedStorageFiles: 0,
        orphanedFavorites: 0
      };

      // 1. Obter todos os chats do Firestore
      const conversationsRef = collection(db, 'usuarios', userId, 'conversas');
      const conversationsSnapshot = await getDocs(conversationsRef);
      const existingChatIds = new Set(conversationsSnapshot.docs.map(doc => doc.id));

      result.totalChatsChecked = existingChatIds.size;

      // 2. Verificar subcoleções órfãs
      await this.auditSubcollections(userId, existingChatIds, result);

      // 3. Verificar arquivos órfãos no Storage
      await this.auditStorageFiles(userId, existingChatIds, result);

      // 4. Verificar favoritos órfãos
      await this.auditOrphanedFavorites(userId, existingChatIds, result);

      console.log('Audit completed:', result);
      return result;
    } catch (error) {
      console.error('Error during audit:', error);
      throw error;
    }
  }

  /**
   * Audita subcoleções órfãs
   */
  private async auditSubcollections(
    userId: string,
    existingChatIds: Set<string>,
    result: AuditResult
  ): Promise<void> {
    try {
      // Verificar subcoleção de estatísticas
      const statsPath = `usuarios/${userId}/conversas`;
      
      // Para cada chat existente, verificar se tem subcoleções
      for (const chatId of existingChatIds) {
        const statsRef = collection(db, 'usuarios', userId, 'conversas', chatId, 'estatisticas');
        const statsSnapshot = await getDocs(statsRef);
        
        if (statsSnapshot.size > 0) {
          // Verificar se o chat principal ainda existe
          const chatRef = doc(db, 'usuarios', userId, 'conversas', chatId);
          const chatDoc = await getDoc(chatRef);
          
          if (!chatDoc.exists()) {
            result.orphanedChatsFound.push(chatId);
            result.orphanedSubcollections += statsSnapshot.size;
          }
        }
      }
    } catch (error) {
      console.error('Error auditing subcollections:', error);
    }
  }

  /**
   * Audita arquivos órfãos no Storage
   */
  private async auditStorageFiles(
    userId: string,
    existingChatIds: Set<string>,
    result: AuditResult
  ): Promise<void> {
    try {
      const userStorageRef = ref(storage, `usuarios/${userId}/conversas`);
      
      try {
        const listResult = await listAll(userStorageRef);
        
        // Verificar cada pasta de chat no Storage
        for (const folderRef of listResult.prefixes) {
          const chatId = folderRef.name;
          
          if (!existingChatIds.has(chatId)) {
            // Chat não existe no Firestore, mas tem arquivos no Storage
            result.orphanedChatsFound.push(chatId);
            
            // Contar arquivos órfãos
            const chatFolderList = await listAll(folderRef);
            result.orphanedStorageFiles += chatFolderList.items.length;
            
            // Contar arquivos em subpastas (anexos)
            for (const subfolderRef of chatFolderList.prefixes) {
              const subfolderList = await listAll(subfolderRef);
              result.orphanedStorageFiles += subfolderList.items.length;
            }
          }
        }
      } catch (error) {
        if ((error as any)?.code !== 'storage/object-not-found') {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error auditing storage files:', error);
    }
  }

  /**
   * Audita favoritos órfãos
   */
  private async auditOrphanedFavorites(
    userId: string,
    existingChatIds: Set<string>,
    result: AuditResult
  ): Promise<void> {
    try {
      const favoritesRef = ref(storage, `usuarios/${userId}/favoritos.json`);
      
      try {
        const bytes = await getBytes(favoritesRef);
        const jsonString = new TextDecoder().decode(bytes);
        const favorites = JSON.parse(jsonString);
        
        // Contar favoritos que referenciam chats inexistentes
        const orphanedFavorites = favorites.filter((fav: any) => 
          !existingChatIds.has(fav.chatId)
        );
        
        result.orphanedFavorites = orphanedFavorites.length;
        
        // Adicionar chats órfãos encontrados nos favoritos
        orphanedFavorites.forEach((fav: any) => {
          if (!result.orphanedChatsFound.includes(fav.chatId)) {
            result.orphanedChatsFound.push(fav.chatId);
          }
        });
      } catch (error) {
        if ((error as any)?.code !== 'storage/object-not-found') {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error auditing orphaned favorites:', error);
    }
  }

  /**
   * Limpa dados órfãos de um chat específico
   */
  async cleanupOrphanedChat(userId: string, chatId: string): Promise<CleanupResult> {
    const result: CleanupResult = {
      chatId,
      orphanedData: {
        subcollections: [],
        storageFiles: [],
        favorites: 0
      },
      cleanupSuccess: true,
      errors: []
    };

    try {
      // 1. Limpar subcoleções órfãs
      await this.cleanupOrphanedSubcollections(userId, chatId, result);

      // 2. Limpar arquivos órfãos do Storage
      await this.cleanupOrphanedStorageFiles(userId, chatId, result);

      // 3. Limpar favoritos órfãos
      await this.cleanupOrphanedFavoritesForChat(userId, chatId, result);

      console.log(`Cleanup completed for chat ${chatId}:`, result);
    } catch (error) {
      result.cleanupSuccess = false;
      result.errors.push(error instanceof Error ? error.message : 'Unknown error');
      console.error(`Error cleaning up chat ${chatId}:`, error);
    }

    return result;
  }

  /**
   * Limpa subcoleções órfãs de um chat
   */
  private async cleanupOrphanedSubcollections(
    userId: string,
    chatId: string,
    result: CleanupResult
  ): Promise<void> {
    try {
      const batch = writeBatch(db);
      let operationCount = 0;

      // Limpar estatísticas
      const statsRef = collection(db, 'usuarios', userId, 'conversas', chatId, 'estatisticas');
      const statsSnapshot = await getDocs(statsRef);

      statsSnapshot.docs.forEach(docSnapshot => {
        batch.delete(docSnapshot.ref);
        result.orphanedData.subcollections.push(docSnapshot.ref.path);
        operationCount++;
      });

      if (operationCount > 0) {
        await batch.commit();
      }
    } catch (error) {
      result.errors.push(`Subcollections cleanup failed: ${error}`);
    }
  }

  /**
   * Limpa arquivos órfãos do Storage
   */
  private async cleanupOrphanedStorageFiles(
    userId: string,
    chatId: string,
    result: CleanupResult
  ): Promise<void> {
    try {
      const chatFolderRef = ref(storage, `usuarios/${userId}/conversas/${chatId}`);
      
      try {
        const listResult = await listAll(chatFolderRef);
        
        // Deletar todos os arquivos
        for (const itemRef of listResult.items) {
          await deleteObject(itemRef);
          result.orphanedData.storageFiles.push(itemRef.fullPath);
        }
        
        // Deletar arquivos em subpastas
        for (const folderRef of listResult.prefixes) {
          const subListResult = await listAll(folderRef);
          for (const itemRef of subListResult.items) {
            await deleteObject(itemRef);
            result.orphanedData.storageFiles.push(itemRef.fullPath);
          }
        }
      } catch (error) {
        if ((error as any)?.code !== 'storage/object-not-found') {
          throw error;
        }
      }
    } catch (error) {
      result.errors.push(`Storage cleanup failed: ${error}`);
    }
  }

  /**
   * Limpa favoritos órfãos para um chat específico
   */
  private async cleanupOrphanedFavoritesForChat(
    userId: string,
    chatId: string,
    result: CleanupResult
  ): Promise<void> {
    try {
      const favoritesRef = ref(storage, `usuarios/${userId}/favoritos.json`);
      
      try {
        const bytes = await getBytes(favoritesRef);
        const jsonString = new TextDecoder().decode(bytes);
        const favorites = JSON.parse(jsonString);
        
        const originalCount = favorites.length;
        const filteredFavorites = favorites.filter((fav: any) => fav.chatId !== chatId);
        
        if (filteredFavorites.length !== originalCount) {
          const updatedData = new TextEncoder().encode(JSON.stringify(filteredFavorites, null, 2));
          await uploadBytes(favoritesRef, updatedData);
          result.orphanedData.favorites = originalCount - filteredFavorites.length;
        }
      } catch (error) {
        if ((error as any)?.code !== 'storage/object-not-found') {
          return; // Arquivo não existe, não há favoritos para limpar
        }
        throw error;
      }
    } catch (error) {
      result.errors.push(`Favorites cleanup failed: ${error}`);
    }
  }

  /**
   * Executa limpeza completa para todos os chats órfãos de um usuário
   */
  async cleanupAllOrphanedData(userId: string): Promise<CleanupResult[]> {
    try {
      const auditResult = await this.auditUserChats(userId);
      const cleanupResults: CleanupResult[] = [];

      for (const chatId of auditResult.orphanedChatsFound) {
        const cleanupResult = await this.cleanupOrphanedChat(userId, chatId);
        cleanupResults.push(cleanupResult);
      }

      return cleanupResults;
    } catch (error) {
      console.error('Error during complete cleanup:', error);
      throw error;
    }
  }
}

export const chatCleanupService = new ChatCleanupServiceImpl();
