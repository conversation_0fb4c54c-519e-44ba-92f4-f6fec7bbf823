{"name": "rafthoria", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "firebase": "^11.9.1", "katex": "^0.16.22", "lucide-react": "^0.519.0", "next": "14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-katex-svelte": "^1.2.0", "remark-directive": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.0.0", "@next/env": "^14.1.0", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "firebase-frameworks": "^0.11.7", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}