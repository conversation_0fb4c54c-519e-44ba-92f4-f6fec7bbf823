// Serviço que usa Firebase Storage para armazenar favoritos
// Similar ao chatStorageService, evita problemas de permissão do Firestore
import { FavoriteMessage, ChatMessage } from './types/chat';
import { storage } from './firebase';
import { ref, getBytes, uploadBytes } from 'firebase/storage';

class ServerFavoritesService {
  // Caminho base para favoritos no Storage
  private getFavoritesPath(userId: string): string {
    return `usuarios/${userId}/favoritos.json`;
  }

  // Carregar favoritos do Storage diretamente
  private async loadFavorites(userId: string): Promise<FavoriteMessage[]> {
    try {
      // Importar dinamicamente o Firebase Storage
      const { storage } = await import('@/lib/firebase');
      const { ref, getDownloadURL } = await import('firebase/storage');

      // Criar referência no Storage
      const favoritesRef = ref(storage, `usuarios/${userId}/favoritos.json`);

      // Obter URL de download
      const downloadURL = await getDownloadURL(favoritesRef);

      // Buscar dados
      const response = await fetch(downloadURL);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.favorites || [];
    } catch (error) {
      console.log('Favoritos não encontrados ou erro ao carregar, retornando lista vazia:', error);
      return [];
    }
  }

  // Salvar favoritos no Storage diretamente
  private async saveFavorites(userId: string, favorites: FavoriteMessage[]): Promise<void> {
    try {
      const data = {
        favorites,
        lastUpdated: Date.now()
      };

      // Importar dinamicamente o Firebase Storage
      const { storage } = await import('@/lib/firebase');
      const { ref, uploadBytes } = await import('firebase/storage');

      // Converter dados para JSON
      const jsonData = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });

      // Criar referência no Storage
      const favoritesRef = ref(storage, `usuarios/${userId}/favoritos.json`);

      // Upload do arquivo
      await uploadBytes(favoritesRef, blob);

      console.log(`Favorites saved for user: ${userId}`);
    } catch (error) {
      console.error('Erro ao salvar favoritos:', error);
      throw error;
    }
  }

  // Adicionar mensagem aos favoritos
  async addToFavorites(
    userId: string,
    messageId: string,
    chatId: string,
    chatName: string,
    message: ChatMessage
  ): Promise<void> {
    try {
      const favorites = await this.loadFavorites(userId);
      const favoriteId = `${chatId}_${messageId}`;

      // Verificar se já existe
      const existingIndex = favorites.findIndex(fav => fav.id === favoriteId);
      if (existingIndex !== -1) {
        return; // Já está nos favoritos
      }

      const favoriteData: FavoriteMessage = {
        id: favoriteId,
        messageId,
        chatId,
        chatName,
        role: message.role,
        content: message.content,
        timestamp: message.timestamp,
        favoritedAt: Date.now(),
        ...(message.attachments && message.attachments.length > 0 && { attachments: message.attachments })
      };

      favorites.unshift(favoriteData); // Adicionar no início
      await this.saveFavorites(userId, favorites);

      console.log('Mensagem adicionada aos favoritos:', favoriteId);
    } catch (error) {
      console.error('Erro ao adicionar mensagem aos favoritos:', error);
      throw error;
    }
  }

  // Remover mensagem dos favoritos
  async removeFromFavorites(userId: string, messageId: string, chatId: string): Promise<void> {
    try {
      const favorites = await this.loadFavorites(userId);
      const favoriteId = `${chatId}_${messageId}`;

      const filteredFavorites = favorites.filter(fav => fav.id !== favoriteId);

      if (filteredFavorites.length === favorites.length) {
        return; // Não estava nos favoritos
      }

      await this.saveFavorites(userId, filteredFavorites);

      console.log('Mensagem removida dos favoritos:', favoriteId);
    } catch (error) {
      console.error('Erro ao remover mensagem dos favoritos:', error);
      throw error;
    }
  }

  // Verificar se uma mensagem está nos favoritos
  async isFavorite(userId: string, messageId: string, chatId: string): Promise<boolean> {
    try {
      const favorites = await this.loadFavorites(userId);
      const favoriteId = `${chatId}_${messageId}`;

      return favorites.some(fav => fav.id === favoriteId);
    } catch (error) {
      console.error('Erro ao verificar se mensagem está nos favoritos:', error);
      return false;
    }
  }

  // Obter todas as mensagens favoritas do usuário
  async getFavorites(userId: string): Promise<FavoriteMessage[]> {
    try {
      const favorites = await this.loadFavorites(userId);
      return favorites.sort((a, b) => b.favoritedAt - a.favoritedAt);
    } catch (error) {
      console.error('Erro ao obter mensagens favoritas:', error);
      throw error;
    }
  }

  // Obter favoritos filtrados por chat
  async getFavoritesByChat(userId: string, chatId: string): Promise<FavoriteMessage[]> {
    try {
      const favorites = await this.loadFavorites(userId);
      return favorites
        .filter(fav => fav.chatId === chatId)
        .sort((a, b) => b.favoritedAt - a.favoritedAt);
    } catch (error) {
      console.error('Erro ao obter favoritos por chat:', error);
      throw error;
    }
  }

  // Obter favoritos filtrados por período
  async getFavoritesByDateRange(
    userId: string,
    startDate: number,
    endDate: number
  ): Promise<FavoriteMessage[]> {
    try {
      const favorites = await this.loadFavorites(userId);
      return favorites
        .filter(fav => fav.favoritedAt >= startDate && fav.favoritedAt <= endDate)
        .sort((a, b) => b.favoritedAt - a.favoritedAt);
    } catch (error) {
      console.error('Erro ao obter favoritos por período:', error);
      throw error;
    }
  }

  // Obter favoritos filtrados por tipo (user/assistant)
  async getFavoritesByRole(userId: string, role: 'user' | 'assistant'): Promise<FavoriteMessage[]> {
    try {
      const favorites = await this.loadFavorites(userId);
      return favorites
        .filter(fav => fav.role === role)
        .sort((a, b) => b.favoritedAt - a.favoritedAt);
    } catch (error) {
      console.error('Erro ao obter favoritos por role:', error);
      throw error;
    }
  }

  // Salvar favoritos diretamente (para limpeza de órfãos)
  async saveFavoritesDirectly(userId: string, favorites: FavoriteMessage[]): Promise<void> {
    try {
      const data = {
        favorites,
        lastUpdated: Date.now()
      };

      const favoritesRef = ref(storage, `usuarios/${userId}/favoritos.json`);
      const updatedData = new TextEncoder().encode(JSON.stringify(data, null, 2));
      await uploadBytes(favoritesRef, updatedData);
      console.log(`Favoritos salvos diretamente para usuário ${userId}: ${favorites.length} itens`);
    } catch (error) {
      console.error('Erro ao salvar favoritos diretamente:', error);
      throw error;
    }
  }
}

export const serverFavoritesService = new ServerFavoritesService();
export default serverFavoritesService;
