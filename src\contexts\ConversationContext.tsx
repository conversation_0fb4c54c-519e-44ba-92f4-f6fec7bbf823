'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import {
  getUserConversations,
  Conversation,
  getConversationsGroupedByFolder
} from '@/lib/conversationService';

interface ConversationContextType {
  conversations: Conversation[];
  selectedConversation: Conversation | null;
  loading: boolean;
  error: string | null;
  refreshConversations: () => Promise<void>;
  selectConversation: (conversation: Conversation | null) => void;
  selectConversationWithPassword: (conversation: Conversation, password?: string) => Promise<boolean>;
  addConversation: (conversation: Conversation) => void;
  updateConversation: (conversationId: string, updates: Partial<Conversation>) => void;
  removeConversation: (conversationId: string) => void;
  lastRefresh: number;
  // Funcionalidades relacionadas às pastas
  getGroupedConversations: () => Promise<{
    folderedConversations: { [folderId: string]: Conversation[] };
    unorganizedConversations: Conversation[];
  }>;
}

const ConversationContext = createContext<ConversationContextType | undefined>(undefined);

export const useConversations = () => {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error('useConversations must be used within a ConversationProvider');
  }
  return context;
};

interface ConversationProviderProps {
  children: React.ReactNode;
}

export const ConversationProvider: React.FC<ConversationProviderProps> = ({ children }) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState(0);
  const { user } = useAuth();

  // Cache para evitar requisições desnecessárias
  const CACHE_DURATION = 30000; // 30 segundos

  const refreshConversations = useCallback(async (force = false) => {
    if (!user) {
      setConversations([]);
      setSelectedConversation(null);
      return;
    }

    // Verificar se precisa atualizar (cache)
    const now = Date.now();
    if (!force && (now - lastRefresh) < CACHE_DURATION && conversations.length > 0) {
      return; // Usar cache
    }

    setLoading(true);
    setError(null);

    try {
      // Forçar bypass do cache no service quando force=true
      const userConversations = await getUserConversations(user.uid, !force);
      setConversations(userConversations);
      setLastRefresh(now);

      // Se a conversa selecionada não existe mais na lista, limpar seleção
      if (selectedConversation && !userConversations.find(c => c.id === selectedConversation.id)) {
        setSelectedConversation(null);
      }

      console.log(`🔄 Conversas atualizadas: ${userConversations.length} conversas carregadas`);

    } catch (err) {
      console.error('Erro ao carregar conversas:', err);
      setError('Erro ao carregar conversas');
    } finally {
      setLoading(false);
    }
  }, [user, lastRefresh, conversations.length, selectedConversation]);

  const selectConversation = useCallback((conversation: Conversation | null) => {
    setSelectedConversation(conversation);
  }, []);

  const selectConversationWithPassword = useCallback(async (conversation: Conversation, password?: string): Promise<boolean> => {
    // Se a conversa não tem senha, selecionar diretamente
    if (!conversation.password) {
      setSelectedConversation(conversation);
      return true;
    }

    // Se a senha foi fornecida, verificar se está correta
    if (password !== undefined) {
      if (password === conversation.password) {
        setSelectedConversation(conversation);
        return true;
      } else {
        return false; // Senha incorreta
      }
    }

    // Se a conversa tem senha mas nenhuma foi fornecida, retornar false para mostrar o modal
    return false;
  }, []);

  const addConversation = useCallback((conversation: Conversation) => {
    setConversations(prev => [conversation, ...prev]);
    setSelectedConversation(conversation);
    setLastRefresh(Date.now()); // Atualizar timestamp do cache
  }, []);

  const updateConversation = useCallback((conversationId: string, updates: Partial<Conversation>) => {
    setConversations(prev => 
      prev.map(conv => 
        conv.id === conversationId 
          ? { ...conv, ...updates }
          : conv
      )
    );

    // Atualizar conversa selecionada se for a mesma
    if (selectedConversation?.id === conversationId) {
      setSelectedConversation(prev => prev ? { ...prev, ...updates } : null);
    }

    setLastRefresh(Date.now()); // Atualizar timestamp do cache
  }, [selectedConversation]);

  const removeConversation = useCallback((conversationId: string) => {
    console.log(`🗑️ Removendo conversa ${conversationId} do estado local`);

    setConversations(prev => {
      const filtered = prev.filter(conv => conv.id !== conversationId);
      console.log(`📊 Conversas restantes: ${filtered.length}`);
      return filtered;
    });

    // Se a conversa removida era a selecionada, limpar seleção
    if (selectedConversation?.id === conversationId) {
      setSelectedConversation(null);
      console.log(`🔄 Conversa selecionada limpa`);
    }

    setLastRefresh(Date.now()); // Atualizar timestamp do cache
  }, [selectedConversation]);

  // Obter conversas agrupadas por pastas
  const getGroupedConversations = useCallback(async () => {
    if (!user) {
      return {
        folderedConversations: {},
        unorganizedConversations: []
      };
    }

    try {
      return await getConversationsGroupedByFolder(user.uid, true);
    } catch (error) {
      console.error('Error getting grouped conversations:', error);
      return {
        folderedConversations: {},
        unorganizedConversations: []
      };
    }
  }, [user]);

  // Carregar conversas quando o usuário mudar
  useEffect(() => {
    if (user) {
      refreshConversations(true); // Forçar refresh quando usuário mudar
    } else {
      setConversations([]);
      setSelectedConversation(null);
      setLastRefresh(0);
    }
  }, [user]); // Remover refreshConversations das dependências para evitar loops

  // Função para refresh manual (quando necessário)
  const manualRefresh = useCallback(async () => {
    if (user) {
      return refreshConversations(true);
    }
  }, [user]);

  const value: ConversationContextType = {
    conversations,
    selectedConversation,
    loading,
    error,
    refreshConversations: manualRefresh,
    selectConversation,
    selectConversationWithPassword,
    addConversation,
    updateConversation,
    removeConversation,
    lastRefresh,
    getGroupedConversations
  };

  return (
    <ConversationContext.Provider value={value}>
      {children}
    </ConversationContext.Provider>
  );
};
