import { NextRequest, NextResponse } from 'next/server';
import { serverFavoritesService } from '@/lib/serverFavoritesService';

export const dynamic = 'force-dynamic';

// POST - Limpar favoritos órfãos de um chat específico
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, chatId } = body;

    if (!userId || !chatId) {
      return NextResponse.json(
        { error: 'userId and chatId are required' },
        { status: 400 }
      );
    }

    // Obter favoritos atuais
    const currentFavorites = await serverFavoritesService.getFavorites(userId);
    
    // Filtrar favoritos que não pertencem ao chat deletado
    const orphanedFavorites = currentFavorites.filter(fav => fav.chatId === chatId);
    const filteredFavorites = currentFavorites.filter(fav => fav.chatId !== chatId);
    
    if (orphanedFavorites.length > 0) {
      // Salvar favoritos filtrados
      await serverFavoritesService.saveFavoritesDirectly(userId, filteredFavorites);
      
      console.log(`Cleaned up ${orphanedFavorites.length} orphaned favorites for chat ${chatId}`);
      
      return NextResponse.json({
        success: true,
        cleanedCount: orphanedFavorites.length,
        message: `Removed ${orphanedFavorites.length} orphaned favorites`
      });
    } else {
      return NextResponse.json({
        success: true,
        cleanedCount: 0,
        message: 'No orphaned favorites found'
      });
    }

  } catch (error) {
    console.error('Error cleaning up orphaned favorites:', error);
    return NextResponse.json(
      { error: 'Internal server error during favorites cleanup' },
      { status: 500 }
    );
  }
}
