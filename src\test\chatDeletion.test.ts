import { deleteConversation } from '../lib/conversationService';
import { chatStatisticsService } from '../lib/chatStatisticsService';
import { serverFavoritesService } from '../lib/serverFavoritesService';
import { attachmentService } from '../lib/attachmentService';
import { chatStorageService } from '../lib/chatStorageService';

// Mock Firebase
jest.mock('../lib/firebase', () => ({
  db: {},
  storage: {}
}));

// Mock Firestore functions
jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  doc: jest.fn(),
  getDocs: jest.fn(),
  deleteDoc: jest.fn(),
  writeBatch: jest.fn(() => ({
    delete: jest.fn(),
    commit: jest.fn()
  })),
  query: jest.fn(),
  orderBy: jest.fn()
}));

// Mock Storage functions
jest.mock('firebase/storage', () => ({
  ref: jest.fn(),
  listAll: jest.fn(),
  deleteObject: jest.fn(),
  getBytes: jest.fn(),
  uploadBytes: jest.fn()
}));

describe('Chat Deletion Tests', () => {
  const mockUserId = 'user123';
  const mockChatId = 'chat456';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Chat Deletion', () => {
    test('should delete all chat data including subcollections', async () => {
      const { getDocs, deleteDoc, writeBatch } = require('firebase/firestore');
      const { listAll, deleteObject, getBytes } = require('firebase/storage');

      // Mock subcollection documents
      const mockStatsDoc = { ref: { path: 'stats-doc-path' } };
      getDocs.mockResolvedValue({
        docs: [mockStatsDoc]
      });

      // Mock batch operations
      const mockBatch = {
        delete: jest.fn(),
        commit: jest.fn().mockResolvedValue(undefined)
      };
      writeBatch.mockReturnValue(mockBatch);

      // Mock main document deletion
      deleteDoc.mockResolvedValue(undefined);

      // Mock storage folder listing and deletion
      listAll.mockResolvedValue({
        items: [
          { fullPath: 'usuarios/user123/conversas/chat456/chat.json' },
          { fullPath: 'usuarios/user123/conversas/chat456/anexos/file1.pdf' }
        ],
        prefixes: []
      });
      deleteObject.mockResolvedValue(undefined);

      // Mock favorites file (empty)
      getBytes.mockRejectedValue({ code: 'storage/object-not-found' });

      // Mock attachment service
      jest.spyOn(attachmentService, 'deleteChatAttachments').mockResolvedValue({
        success: true,
        deletedCount: 2,
        errors: []
      });

      await deleteConversation(mockUserId, mockChatId);

      // Verify subcollections were deleted
      expect(mockBatch.delete).toHaveBeenCalledWith(mockStatsDoc.ref);
      expect(mockBatch.commit).toHaveBeenCalled();

      // Verify main document was deleted
      expect(deleteDoc).toHaveBeenCalled();

      // Verify storage files were deleted
      expect(deleteObject).toHaveBeenCalledTimes(2);

      // Verify attachments were deleted
      expect(attachmentService.deleteChatAttachments).toHaveBeenCalledWith(mockUserId, mockChatId);
    });

    test('should handle missing subcollections gracefully', async () => {
      const { getDocs, deleteDoc, writeBatch } = require('firebase/firestore');
      const { listAll, deleteObject, getBytes } = require('firebase/storage');

      // Mock empty subcollections
      getDocs.mockResolvedValue({ docs: [] });

      const mockBatch = {
        delete: jest.fn(),
        commit: jest.fn().mockResolvedValue(undefined)
      };
      writeBatch.mockReturnValue(mockBatch);

      deleteDoc.mockResolvedValue(undefined);
      listAll.mockResolvedValue({ items: [], prefixes: [] });
      getBytes.mockRejectedValue({ code: 'storage/object-not-found' });

      jest.spyOn(attachmentService, 'deleteChatAttachments').mockResolvedValue({
        success: true,
        deletedCount: 0,
        errors: []
      });

      await deleteConversation(mockUserId, mockChatId);

      // Should not call batch.commit if no documents to delete
      expect(mockBatch.delete).not.toHaveBeenCalled();
      expect(deleteDoc).toHaveBeenCalled();
    });

    test('should clean up orphaned favorites', async () => {
      const { getDocs, deleteDoc, writeBatch } = require('firebase/firestore');
      const { listAll, deleteObject, getBytes, uploadBytes } = require('firebase/storage');

      // Mock basic operations
      getDocs.mockResolvedValue({ docs: [] });
      const mockBatch = { delete: jest.fn(), commit: jest.fn() };
      writeBatch.mockReturnValue(mockBatch);
      deleteDoc.mockResolvedValue(undefined);
      listAll.mockResolvedValue({ items: [], prefixes: [] });

      // Mock favorites file with orphaned favorites
      const mockFavorites = [
        { id: 'chat456_msg1', chatId: 'chat456', messageId: 'msg1' },
        { id: 'chat789_msg2', chatId: 'chat789', messageId: 'msg2' }
      ];
      const mockFavoritesData = new TextEncoder().encode(JSON.stringify(mockFavorites));
      getBytes.mockResolvedValue(mockFavoritesData);
      uploadBytes.mockResolvedValue(undefined);

      jest.spyOn(attachmentService, 'deleteChatAttachments').mockResolvedValue({
        success: true,
        deletedCount: 0,
        errors: []
      });

      await deleteConversation(mockUserId, mockChatId);

      // Verify favorites were filtered and saved
      expect(uploadBytes).toHaveBeenCalled();
      const uploadCall = uploadBytes.mock.calls[0];
      const updatedFavorites = JSON.parse(new TextDecoder().decode(uploadCall[1]));
      
      // Should only contain favorites from other chats
      expect(updatedFavorites).toHaveLength(1);
      expect(updatedFavorites[0].chatId).toBe('chat789');
    });

    test('should continue deletion even if some operations fail', async () => {
      const { getDocs, deleteDoc, writeBatch } = require('firebase/firestore');
      const { listAll, deleteObject, getBytes } = require('firebase/storage');

      // Mock subcollection deletion failure
      getDocs.mockRejectedValue(new Error('Firestore error'));
      deleteDoc.mockResolvedValue(undefined);
      listAll.mockResolvedValue({ items: [], prefixes: [] });
      getBytes.mockRejectedValue({ code: 'storage/object-not-found' });

      jest.spyOn(attachmentService, 'deleteChatAttachments').mockResolvedValue({
        success: false,
        deletedCount: 0,
        errors: ['Attachment deletion failed']
      });

      // Should throw error due to subcollection deletion failure
      await expect(deleteConversation(mockUserId, mockChatId)).rejects.toThrow();
    });
  });

  describe('Validation Functions', () => {
    test('should validate complete chat deletion', async () => {
      // This would be implemented as part of the audit function
      // For now, we'll create a basic structure
      const validateChatDeletion = async (userId: string, chatId: string) => {
        const { getDoc } = require('firebase/firestore');
        const { getBytes } = require('firebase/storage');

        // Check if main document exists
        getDoc.mockResolvedValue({ exists: () => false });

        // Check if storage files exist
        getBytes.mockRejectedValue({ code: 'storage/object-not-found' });

        return {
          firestoreClean: true,
          storageClean: true,
          favoritesClean: true
        };
      };

      const result = await validateChatDeletion(mockUserId, mockChatId);
      expect(result.firestoreClean).toBe(true);
      expect(result.storageClean).toBe(true);
      expect(result.favoritesClean).toBe(true);
    });
  });
});
