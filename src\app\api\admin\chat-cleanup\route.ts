import { NextRequest, NextResponse } from 'next/server';
import { chatCleanupService } from '@/lib/chatCleanupService';

export const dynamic = 'force-dynamic';

// GET - Auditar dados órfãos
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const action = searchParams.get('action') || 'audit';

    if (!userId) {
      return NextResponse.json(
        { error: 'userId parameter is required' },
        { status: 400 }
      );
    }

    if (action === 'audit') {
      const auditResult = await chatCleanupService.auditUserChats(userId);
      
      return NextResponse.json({
        success: true,
        data: auditResult,
        message: `Audit completed. Found ${auditResult.orphanedChatsFound.length} orphaned chats.`
      });
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "audit".' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in chat cleanup audit:', error);
    return NextResponse.json(
      { error: 'Internal server error during audit' },
      { status: 500 }
    );
  }
}

// POST - Executar limpeza
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, action, chatId } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'userId is required' },
        { status: 400 }
      );
    }

    if (action === 'cleanup-single' && chatId) {
      // Limpar um chat específico
      const cleanupResult = await chatCleanupService.cleanupOrphanedChat(userId, chatId);
      
      return NextResponse.json({
        success: cleanupResult.cleanupSuccess,
        data: cleanupResult,
        message: cleanupResult.cleanupSuccess 
          ? `Chat ${chatId} cleaned up successfully`
          : `Failed to clean up chat ${chatId}`
      });

    } else if (action === 'cleanup-all') {
      // Limpar todos os dados órfãos
      const cleanupResults = await chatCleanupService.cleanupAllOrphanedData(userId);
      
      const successCount = cleanupResults.filter(r => r.cleanupSuccess).length;
      const totalCount = cleanupResults.length;
      
      return NextResponse.json({
        success: successCount === totalCount,
        data: {
          results: cleanupResults,
          summary: {
            total: totalCount,
            successful: successCount,
            failed: totalCount - successCount
          }
        },
        message: `Cleanup completed. ${successCount}/${totalCount} chats cleaned successfully.`
      });

    } else {
      return NextResponse.json(
        { error: 'Invalid action. Use "cleanup-single" with chatId or "cleanup-all".' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error in chat cleanup:', error);
    return NextResponse.json(
      { error: 'Internal server error during cleanup' },
      { status: 500 }
    );
  }
}

// DELETE - Forçar exclusão completa de um chat (incluindo dados órfãos)
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, chatId } = body;

    if (!userId || !chatId) {
      return NextResponse.json(
        { error: 'userId and chatId are required' },
        { status: 400 }
      );
    }

    // Primeiro tentar exclusão normal
    try {
      const { deleteConversation } = await import('@/lib/conversationService');
      await deleteConversation(userId, chatId);
    } catch (error) {
      console.warn('Normal deletion failed, proceeding with cleanup:', error);
    }

    // Depois executar limpeza de dados órfãos
    const cleanupResult = await chatCleanupService.cleanupOrphanedChat(userId, chatId);

    return NextResponse.json({
      success: cleanupResult.cleanupSuccess,
      data: cleanupResult,
      message: cleanupResult.cleanupSuccess
        ? `Chat ${chatId} forcefully deleted and cleaned up`
        : `Failed to completely clean up chat ${chatId}`
    });

  } catch (error) {
    console.error('Error in forced chat deletion:', error);
    return NextResponse.json(
      { error: 'Internal server error during forced deletion' },
      { status: 500 }
    );
  }
}
